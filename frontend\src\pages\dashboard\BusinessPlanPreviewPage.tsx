import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Download, 
  Share2, 
  Print, 
  Eye,
  FileText,
  Calendar,
  User,
  Building,
  TrendingUp,
  Target,
  DollarSign,
  Users,
  Lightbulb
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store/hooks';
import { businessPlansAPI, BusinessPlan } from '../../services/businessPlanApi';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const BusinessPlanPreviewPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isRTL } = useAppSelector((state) => state.language);
  
  const [businessPlan, setBusinessPlan] = useState<BusinessPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBusinessPlan = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        const plan = await businessPlansAPI.getBusinessPlan(parseInt(id));
        setBusinessPlan(plan);
      } catch (err) {
        console.error('Error fetching business plan:', err);
        setError('Failed to load business plan');
      } finally {
        setLoading(false);
      }
    };

    fetchBusinessPlan();
  }, [id]);

  const handleBack = () => {
    navigate(`/dashboard/business-plans/${id}`);
  };

  const handleDownload = () => {
    // TODO: Implement PDF download functionality
    console.log('Download business plan as PDF');
  };

  const handleShare = () => {
    // TODO: Implement share functionality
    console.log('Share business plan');
  };

  const handlePrint = () => {
    window.print();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !businessPlan) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center">
        <div className="text-center">
          <FileText className="mx-auto mb-4 text-red-400" size={48} />
          <h2 className="text-xl font-semibold text-white mb-2">
            {t('businessPlan.preview.error')}
          </h2>
          <p className="text-gray-400 mb-4">
            {error || t('businessPlan.preview.notFound')}
          </p>
          <button
            onClick={handleBack}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            {t('common.back')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="bg-white/10 backdrop-blur-sm border-b border-white/20 print:hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`flex items-center justify-between py-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <button
                onClick={handleBack}
                className={`flex items-center text-gray-300 hover:text-white transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
              >
                <ArrowLeft size={20} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('businessPlan.backToEditor')}
              </button>
            </div>
            
            <div className={`flex items-center space-x-3 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
              <button
                onClick={handleDownload}
                className="flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Download size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('common.download')}
              </button>
              
              <button
                onClick={handleShare}
                className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Share2 size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('common.share')}
              </button>
              
              <button
                onClick={handlePrint}
                className="flex items-center px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <Print size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('common.print')}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Preview Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-xl print:shadow-none print:bg-white">
          {/* Business Plan Header */}
          <div className="p-8 border-b border-gray-200">
            <div className="text-center mb-6">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {businessPlan.title}
              </h1>
              <div className="flex items-center justify-center text-gray-600 mb-4">
                <Calendar size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
                {t('businessPlan.preview.lastUpdated')}: {new Date(businessPlan.updated_at).toLocaleDateString()}
              </div>
            </div>
            
            {/* Business Plan Metadata */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
              <div className="flex items-center">
                <User size={16} className={`text-gray-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                <div>
                  <div className="font-medium text-gray-900">{t('businessPlan.preview.owner')}</div>
                  <div className="text-gray-600">{businessPlan.owner_name}</div>
                </div>
              </div>
              
              <div className="flex items-center">
                <TrendingUp size={16} className={`text-gray-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                <div>
                  <div className="font-medium text-gray-900">{t('businessPlan.preview.status')}</div>
                  <div className="text-gray-600 capitalize">{businessPlan.status}</div>
                </div>
              </div>
              
              <div className="flex items-center">
                <Target size={16} className={`text-gray-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                <div>
                  <div className="font-medium text-gray-900">{t('businessPlan.preview.completion')}</div>
                  <div className="text-gray-600">{businessPlan.completion_percentage}%</div>
                </div>
              </div>
            </div>
          </div>

          {/* Business Plan Content */}
          <div className="p-8">
            {businessPlan.sections && businessPlan.sections.length > 0 ? (
              businessPlan.sections.map((section, index) => (
                <div key={section.id} className="mb-8 last:mb-0">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                    <span className="bg-purple-100 text-purple-800 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">
                      {index + 1}
                    </span>
                    {section.title}
                  </h2>
                  
                  {section.content ? (
                    <div 
                      className="prose prose-lg max-w-none text-gray-700"
                      dangerouslySetInnerHTML={{ __html: section.content }}
                    />
                  ) : (
                    <div className="text-gray-500 italic py-4">
                      {t('businessPlan.preview.noContent')}
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="text-center py-12">
                <FileText className="mx-auto mb-4 text-gray-400" size={48} />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {t('businessPlan.preview.noSections')}
                </h3>
                <p className="text-gray-600">
                  {t('businessPlan.preview.noSectionsDescription')}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessPlanPreviewPage;
