import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { 
  BusinessPlan, 
  BusinessPlanSection,
  businessPlansAPI,
  businessPlanSectionsAPI 
} from '../services/businessPlanApi';
import { 
  validateBusinessPlanContent, 
  validateBusinessPlanTitle 
} from '../utils/security';

// Define the state interface
interface BusinessPlansState {
  // Business Plans
  businessPlans: BusinessPlan[];
  currentBusinessPlan: BusinessPlan | null;
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
  
  // Business Plan Sections
  sections: BusinessPlanSection[];
  currentSection: BusinessPlanSection | null;
  activeSectionId: number | null;
  isSectionsLoading: boolean;
  isSectionUpdating: boolean;
  sectionsError: string | null;
  
  // Editor State
  sectionContent: string;
  hasUnsavedChanges: boolean;
  isAutoSaving: boolean;
  autoSaveError: boolean;
  lastSavedAt: string | null;
  
  // UI State
  showUnsavedWarning: boolean;
  isGeneratingContent: boolean;
  
  // Filters and Search
  filters: {
    status?: string;
    businessIdea?: number;
    search?: string;
  };
  
  // Pagination
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

// Initial state
const initialState: BusinessPlansState = {
  businessPlans: [],
  currentBusinessPlan: null,
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
  
  sections: [],
  currentSection: null,
  activeSectionId: null,
  isSectionsLoading: false,
  isSectionUpdating: false,
  sectionsError: null,
  
  sectionContent: '',
  hasUnsavedChanges: false,
  isAutoSaving: false,
  autoSaveError: false,
  lastSavedAt: null,
  
  showUnsavedWarning: false,
  isGeneratingContent: false,
  
  filters: {},
  
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0,
    hasNext: false,
    hasPrevious: false,
  },
};

// Async Thunks for Business Plans
export const fetchBusinessPlans = createAsyncThunk(
  'businessPlans/fetchBusinessPlans',
  async (params?: { businessIdeaId?: number; filters?: any }, { rejectWithValue }) => {
    try {
      const businessPlans = await businessPlansAPI.getPlans(params?.businessIdeaId);
      return businessPlans;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch business plans');
    }
  }
);

export const fetchBusinessPlan = createAsyncThunk(
  'businessPlans/fetchBusinessPlan',
  async (id: number, { rejectWithValue }) => {
    try {
      const businessPlan = await businessPlansAPI.getPlan(id);
      return businessPlan;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch business plan');
    }
  }
);

export const createBusinessPlan = createAsyncThunk(
  'businessPlans/createBusinessPlan',
  async (data: Partial<BusinessPlan>, { rejectWithValue }) => {
    try {
      // Validate title before creating
      if (data.title) {
        const { isValid, sanitized, error } = validateBusinessPlanTitle(data.title);
        if (!isValid) {
          return rejectWithValue(error || 'Invalid title');
        }
        data.title = sanitized;
      }
      
      const businessPlan = await businessPlansAPI.createPlan(data);
      return businessPlan;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create business plan');
    }
  }
);

export const updateBusinessPlan = createAsyncThunk(
  'businessPlans/updateBusinessPlan',
  async ({ id, data }: { id: number; data: Partial<BusinessPlan> }, { rejectWithValue }) => {
    try {
      // Validate title if provided
      if (data.title) {
        const { isValid, sanitized, error } = validateBusinessPlanTitle(data.title);
        if (!isValid) {
          return rejectWithValue(error || 'Invalid title');
        }
        data.title = sanitized;
      }
      
      const businessPlan = await businessPlansAPI.updatePlan(id, data);
      return businessPlan;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update business plan');
    }
  }
);

export const deleteBusinessPlan = createAsyncThunk(
  'businessPlans/deleteBusinessPlan',
  async (id: number, { rejectWithValue }) => {
    try {
      await businessPlansAPI.deletePlan(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete business plan');
    }
  }
);

// Async Thunks for Business Plan Sections
export const fetchBusinessPlanSections = createAsyncThunk(
  'businessPlans/fetchBusinessPlanSections',
  async (planId: number, { rejectWithValue }) => {
    try {
      const sections = await businessPlanSectionsAPI.getSections(planId);
      return sections.sort((a, b) => (a.order || 0) - (b.order || 0));
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch business plan sections');
    }
  }
);

export const updateBusinessPlanSection = createAsyncThunk(
  'businessPlans/updateBusinessPlanSection',
  async ({ 
    sectionId, 
    content, 
    planId 
  }: { 
    sectionId: number; 
    content: string; 
    planId: number;
  }, { rejectWithValue }) => {
    try {
      // Validate and sanitize content
      const { isValid, sanitized, error } = validateBusinessPlanContent(content);
      if (!isValid) {
        return rejectWithValue(error || 'Invalid content');
      }
      
      const section = await businessPlanSectionsAPI.updateSection(sectionId, { content: sanitized });
      return { section, planId };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update section');
    }
  }
);

export const autoSaveSection = createAsyncThunk(
  'businessPlans/autoSaveSection',
  async ({ 
    sectionId, 
    content, 
    planId 
  }: { 
    sectionId: number; 
    content: string; 
    planId: number;
  }, { rejectWithValue }) => {
    try {
      // Validate and sanitize content
      const { isValid, sanitized, error } = validateBusinessPlanContent(content);
      if (!isValid) {
        return rejectWithValue(error || 'Invalid content');
      }
      
      const section = await businessPlanSectionsAPI.updateSection(sectionId, { content: sanitized });
      return { section, planId };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Auto-save failed');
    }
  }
);

// Create the slice
const businessPlansSlice = createSlice({
  name: 'businessPlans',
  initialState,
  reducers: {
    // UI State Management
    setActiveSectionId: (state, action: PayloadAction<number | null>) => {
      state.activeSectionId = action.payload;
      if (action.payload) {
        const section = state.sections.find(s => s.id === action.payload);
        state.currentSection = section || null;
        state.sectionContent = section?.content || '';
      } else {
        state.currentSection = null;
        state.sectionContent = '';
      }
      state.hasUnsavedChanges = false;
    },
    
    setSectionContent: (state, action: PayloadAction<string>) => {
      state.sectionContent = action.payload;
      state.hasUnsavedChanges = true;
    },
    
    setHasUnsavedChanges: (state, action: PayloadAction<boolean>) => {
      state.hasUnsavedChanges = action.payload;
    },
    
    setShowUnsavedWarning: (state, action: PayloadAction<boolean>) => {
      state.showUnsavedWarning = action.payload;
    },
    
    setIsGeneratingContent: (state, action: PayloadAction<boolean>) => {
      state.isGeneratingContent = action.payload;
    },
    
    clearAutoSaveError: (state) => {
      state.autoSaveError = false;
    },
    
    // Filters and Search
    setFilters: (state, action: PayloadAction<Partial<typeof initialState.filters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    clearFilters: (state) => {
      state.filters = {};
    },
    
    // Error Management
    clearError: (state) => {
      state.error = null;
    },
    
    clearSectionsError: (state) => {
      state.sectionsError = null;
    },
    
    // Reset State
    resetBusinessPlanState: (state) => {
      state.currentBusinessPlan = null;
      state.sections = [];
      state.currentSection = null;
      state.activeSectionId = null;
      state.sectionContent = '';
      state.hasUnsavedChanges = false;
      state.showUnsavedWarning = false;
      state.isGeneratingContent = false;
      state.error = null;
      state.sectionsError = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch Business Plans
    builder.addCase(fetchBusinessPlans.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchBusinessPlans.fulfilled, (state, action) => {
      state.isLoading = false;
      state.businessPlans = action.payload;
    });
    builder.addCase(fetchBusinessPlans.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });
    
    // Fetch Single Business Plan
    builder.addCase(fetchBusinessPlan.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchBusinessPlan.fulfilled, (state, action) => {
      state.isLoading = false;
      state.currentBusinessPlan = action.payload;
    });
    builder.addCase(fetchBusinessPlan.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });
    
    // Create Business Plan
    builder.addCase(createBusinessPlan.pending, (state) => {
      state.isCreating = true;
      state.error = null;
    });
    builder.addCase(createBusinessPlan.fulfilled, (state, action) => {
      state.isCreating = false;
      state.businessPlans.unshift(action.payload);
      state.currentBusinessPlan = action.payload;
    });
    builder.addCase(createBusinessPlan.rejected, (state, action) => {
      state.isCreating = false;
      state.error = action.payload as string;
    });
    
    // Update Business Plan
    builder.addCase(updateBusinessPlan.pending, (state) => {
      state.isUpdating = true;
      state.error = null;
    });
    builder.addCase(updateBusinessPlan.fulfilled, (state, action) => {
      state.isUpdating = false;
      const index = state.businessPlans.findIndex(plan => plan.id === action.payload.id);
      if (index !== -1) {
        state.businessPlans[index] = action.payload;
      }
      if (state.currentBusinessPlan?.id === action.payload.id) {
        state.currentBusinessPlan = action.payload;
      }
    });
    builder.addCase(updateBusinessPlan.rejected, (state, action) => {
      state.isUpdating = false;
      state.error = action.payload as string;
    });
    
    // Delete Business Plan
    builder.addCase(deleteBusinessPlan.pending, (state) => {
      state.isDeleting = true;
      state.error = null;
    });
    builder.addCase(deleteBusinessPlan.fulfilled, (state, action) => {
      state.isDeleting = false;
      state.businessPlans = state.businessPlans.filter(plan => plan.id !== action.payload);
      if (state.currentBusinessPlan?.id === action.payload) {
        state.currentBusinessPlan = null;
      }
    });
    builder.addCase(deleteBusinessPlan.rejected, (state, action) => {
      state.isDeleting = false;
      state.error = action.payload as string;
    });
    
    // Fetch Business Plan Sections
    builder.addCase(fetchBusinessPlanSections.pending, (state) => {
      state.isSectionsLoading = true;
      state.sectionsError = null;
    });
    builder.addCase(fetchBusinessPlanSections.fulfilled, (state, action) => {
      state.isSectionsLoading = false;
      state.sections = action.payload;
    });
    builder.addCase(fetchBusinessPlanSections.rejected, (state, action) => {
      state.isSectionsLoading = false;
      state.sectionsError = action.payload as string;
    });
    
    // Update Business Plan Section
    builder.addCase(updateBusinessPlanSection.pending, (state) => {
      state.isSectionUpdating = true;
      state.sectionsError = null;
    });
    builder.addCase(updateBusinessPlanSection.fulfilled, (state, action) => {
      state.isSectionUpdating = false;
      state.hasUnsavedChanges = false;
      state.lastSavedAt = new Date().toISOString();
      
      const index = state.sections.findIndex(section => section.id === action.payload.section.id);
      if (index !== -1) {
        state.sections[index] = action.payload.section;
      }
      if (state.currentSection?.id === action.payload.section.id) {
        state.currentSection = action.payload.section;
      }
    });
    builder.addCase(updateBusinessPlanSection.rejected, (state, action) => {
      state.isSectionUpdating = false;
      state.sectionsError = action.payload as string;
    });
    
    // Auto Save Section
    builder.addCase(autoSaveSection.pending, (state) => {
      state.isAutoSaving = true;
      state.autoSaveError = false;
    });
    builder.addCase(autoSaveSection.fulfilled, (state, action) => {
      state.isAutoSaving = false;
      state.hasUnsavedChanges = false;
      state.lastSavedAt = new Date().toISOString();
      
      const index = state.sections.findIndex(section => section.id === action.payload.section.id);
      if (index !== -1) {
        state.sections[index] = action.payload.section;
      }
      if (state.currentSection?.id === action.payload.section.id) {
        state.currentSection = action.payload.section;
      }
    });
    builder.addCase(autoSaveSection.rejected, (state, action) => {
      state.isAutoSaving = false;
      state.autoSaveError = true;
    });
  },
});

// Export actions
export const {
  setActiveSectionId,
  setSectionContent,
  setHasUnsavedChanges,
  setShowUnsavedWarning,
  setIsGeneratingContent,
  clearAutoSaveError,
  setFilters,
  clearFilters,
  clearError,
  clearSectionsError,
  resetBusinessPlanState,
} = businessPlansSlice.actions;

// Export selectors
export const selectBusinessPlans = (state: { businessPlans: BusinessPlansState }) => state.businessPlans.businessPlans;
export const selectCurrentBusinessPlan = (state: { businessPlans: BusinessPlansState }) => state.businessPlans.currentBusinessPlan;
export const selectBusinessPlanSections = (state: { businessPlans: BusinessPlansState }) => state.businessPlans.sections;
export const selectCurrentSection = (state: { businessPlans: BusinessPlansState }) => state.businessPlans.currentSection;
export const selectActiveSectionId = (state: { businessPlans: BusinessPlansState }) => state.businessPlans.activeSectionId;
export const selectSectionContent = (state: { businessPlans: BusinessPlansState }) => state.businessPlans.sectionContent;
export const selectHasUnsavedChanges = (state: { businessPlans: BusinessPlansState }) => state.businessPlans.hasUnsavedChanges;
export const selectIsLoading = (state: { businessPlans: BusinessPlansState }) => state.businessPlans.isLoading;
export const selectIsAutoSaving = (state: { businessPlans: BusinessPlansState }) => state.businessPlans.isAutoSaving;
export const selectAutoSaveError = (state: { businessPlans: BusinessPlansState }) => state.businessPlans.autoSaveError;
export const selectIsGeneratingContent = (state: { businessPlans: BusinessPlansState }) => state.businessPlans.isGeneratingContent;

// Export reducer
export default businessPlansSlice.reducer;