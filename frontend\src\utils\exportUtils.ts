import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { autoTable } from 'jspdf-autotable';

/**
 * Export data to CSV file
 * @param data Array of objects to export
 * @param filename Filename without extension
 */
export const exportToCSV = (data: any[], filename: string) => {
  if (!data || !data.length) {
    console.error('No data to export');
    return;
  }

  // Get headers from the first object
  const headers = Object.keys(data[0]);

  // Convert data to CSV format
  const csvContent = [
    headers.join(','), // Header row
    ...data.map(row =>
      headers.map(header => {
        // Handle values that might contain commas or quotes
        const value = row[header]?.toString() || '';
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',')
    )
  ].join('\n');

  // Create a blob and download link
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Export data to PDF file
 * @param columns Array of column definitions with header and dataKey
 * @param data Array of objects to export
 * @param title Title of the PDF
 * @param subtitle Optional subtitle
 * @param filename Filename without extension
 */
export const exportToPDF = (
  columns: Array<{ header: string; dataKey: string }>,
  data: any[],
  title: string,
  subtitle?: string,
  filename?: string
) => {
  if (!data || !data.length) {
    console.error('No data to export');
    // Show user-friendly message instead of silent failure
    alert('No data available to export. Please add content to your business plan first.');
    return;
  }

  // Create a new PDF document
  const doc = new jsPDF();

  // Add title
  doc.setFontSize(18);
  doc.text(title, 14, 22);

  // Add subtitle if provided
  if (subtitle) {
    doc.setFontSize(12);
    doc.text(subtitle, 14, 30);
  }

  // Add date
  doc.setFontSize(10);
  doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 14, subtitle ? 38 : 30);

  // Add table
  autoTable(doc, {
    startY: subtitle ? 42 : 34,
    head: [columns.map(col => col.header)],
    body: data.map(row => columns.map(col => row[col.dataKey])),
    theme: 'grid',
    styles: {
      fontSize: 10,
      cellPadding: 3,
      lineColor: [200, 200, 200],
      lineWidth: 0.1,
    },
    headStyles: {
      fillColor: [75, 75, 75],
      textColor: [255, 255, 255],
      fontStyle: 'bold',
    },
    alternateRowStyles: {
      fillColor: [240, 240, 240],
    },
  });

  // Save the PDF
  doc.save(`${filename || 'export'}.pdf`);

  // Show success message
  console.log(`PDF exported successfully: ${filename || 'export'}.pdf`);
};

/**
 * Export data to Excel file
 * @param data Array of objects to export
 * @param sheetName Name of the Excel sheet
 * @param filename Filename without extension
 */
export const exportToExcel = async (data: any[], sheetName: string, filename: string) => {
  try {
    // Dynamically import xlsx library
    const XLSX = await import('xlsx');

    // Create a new workbook
    const wb = XLSX.utils.book_new();

    // Convert data to worksheet
    const ws = XLSX.utils.json_to_sheet(data);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, sheetName);

    // Write to file and trigger download
    XLSX.writeFile(wb, `${filename}.xlsx`);
  } catch (error) {
    console.error('Error exporting to Excel:', error);
  }
};

/**
 * Format date for export
 * @param date Date string or Date object
 * @param format Format string (default: 'MM/DD/YYYY')
 * @returns Formatted date string
 */
export const formatDateForExport = (date: string | Date, format: string = 'MM/DD/YYYY'): string => {
  if (!date) return '';

  const d = typeof date === 'string' ? new Date(date) : date;

  // Check if date is valid
  if (isNaN(d.getTime())) return '';

  const month = d.getMonth() + 1;
  const day = d.getDate();
  const year = d.getFullYear();

  // Replace format tokens with actual values
  return format
    .replace('MM', month.toString().padStart(2, '0'))
    .replace('DD', day.toString().padStart(2, '0'))
    .replace('YYYY', year.toString());
};

/**
 * Export analytics data to PDF
 *
 * @param analyticsData Analytics data object
 * @param businessIdeaTitle Business idea title
 * @param filename Filename for the PDF (without extension)
 */
export const exportAnalyticsToPDF = (
  analyticsData: any,
  businessIdeaTitle: string,
  filename?: string
) => {
  // Create a new PDF document
  const doc = new jsPDF();

  // Set document properties
  doc.setProperties({
    title: `Analytics Report - ${businessIdeaTitle}`,
    subject: 'Business Analytics Report',
    author: 'Yasmeen AI Platform',
    keywords: 'business, analytics, report',
    creator: 'Yasmeen AI Platform'
  });

  // Add title
  doc.setFontSize(20);
  doc.setTextColor(40, 40, 40);
  doc.text(`Analytics Report - ${businessIdeaTitle}`, 14, 22);

  // Add date
  const date = new Date().toLocaleDateString();
  doc.setFontSize(10);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated on: ${date}`, 14, 32);

  // Add current metrics section
  doc.setFontSize(16);
  doc.setTextColor(40, 40, 40);
  doc.text('Current Metrics', 14, 42);

  // Extract business analytics data
  const businessAnalytics = analyticsData.business_analytics || {};

  // Create current metrics table
  const currentMetricsData = [
    { metric: 'Progress Rate', value: `${businessAnalytics.progress_rate || 0} updates/month` },
    { metric: 'Milestone Completion', value: `${businessAnalytics.milestone_completion_rate || 0}%` },
    { metric: 'Goal Achievement', value: `${businessAnalytics.goal_achievement_rate || 0}%` },
    { metric: 'Team Size', value: businessAnalytics.team_size || 1 },
    { metric: 'Mentor Engagement', value: `${businessAnalytics.mentor_engagement || 0}%` }
  ];

  autoTable(doc, {
    startY: 46,
    head: [['Metric', 'Value']],
    body: currentMetricsData.map(row => [row.metric, row.value]),
    theme: 'striped',
    headStyles: {
      fillColor: [75, 58, 176],
      textColor: [255, 255, 255],
      fontStyle: 'bold'
    },
    styles: {
      fontSize: 10,
      cellPadding: 3
    }
  });

  // Add predictive analytics section if available
  const predictiveAnalytics = analyticsData.predictive_analytics || {};
  if (predictiveAnalytics && Object.keys(predictiveAnalytics).length > 0) {
    const finalY = (doc as any).lastAutoTable.finalY || 100;

    doc.setFontSize(16);
    doc.setTextColor(40, 40, 40);
    doc.text('Predictive Analytics', 14, finalY + 10);

    // Create predictive metrics table
    const predictiveMetricsData = [
      { metric: 'Success Probability', value: `${predictiveAnalytics.success_probability || 0}%` },
      { metric: 'Prediction Confidence', value: predictiveAnalytics.prediction_confidence || 'Low' }
    ];

    autoTable(doc, {
      startY: finalY + 14,
      head: [['Metric', 'Value']],
      body: predictiveMetricsData.map(row => [row.metric, row.value]),
      theme: 'striped',
      headStyles: {
        fillColor: [75, 58, 176],
        textColor: [255, 255, 255],
        fontStyle: 'bold'
      },
      styles: {
        fontSize: 10,
        cellPadding: 3
      }
    });

    // Add risk factors if available
    if (predictiveAnalytics.risk_factors &&
        predictiveAnalytics.risk_factors.high_risks &&
        predictiveAnalytics.risk_factors.high_risks.length > 0) {

      const finalY2 = (doc as any).lastAutoTable.finalY || 150;

      doc.setFontSize(14);
      doc.setTextColor(40, 40, 40);
      doc.text('Key Risk Factors', 14, finalY2 + 10);

      // Create risk factors table
      const riskFactorsData = predictiveAnalytics.risk_factors.high_risks.map((risk: any) => ({
        title: risk.title,
        description: risk.description,
        mitigation: risk.mitigation
      }));

      autoTable(doc, {
        startY: finalY2 + 14,
        head: [['Risk', 'Description', 'Mitigation']],
        body: riskFactorsData.map(row => [row.title, row.description, row.mitigation]),
        theme: 'striped',
        headStyles: {
          fillColor: [75, 58, 176],
          textColor: [255, 255, 255],
          fontStyle: 'bold'
        },
        styles: {
          fontSize: 10,
          cellPadding: 3
        }
      });
    }
  }

  // Add comparative analytics section if available
  const comparativeAnalytics = analyticsData.comparative_analytics || {};
  if (comparativeAnalytics && Object.keys(comparativeAnalytics).length > 0) {
    // Check if we need a new page
    const finalY = (doc as any).lastAutoTable.finalY || 200;
    if (finalY > 200) {
      doc.addPage();
    } else {
      doc.setFontSize(16);
      doc.setTextColor(40, 40, 40);
      doc.text('Comparative Analytics', 14, finalY + 10);
    }

    // Create percentile rankings table if available
    if (comparativeAnalytics.percentile_rankings &&
        comparativeAnalytics.percentile_rankings.percentiles) {

      const percentiles = comparativeAnalytics.percentile_rankings.percentiles;
      const percentileData = [
        { metric: 'Progress Rate', percentile: `${Math.round(percentiles.progress_rate || 0)}%` },
        { metric: 'Milestone Completion', percentile: `${Math.round(percentiles.milestone_completion_rate || 0)}%` },
        { metric: 'Goal Achievement', percentile: `${Math.round(percentiles.goal_achievement_rate || 0)}%` },
        { metric: 'Team Size', percentile: `${Math.round(percentiles.team_size || 0)}%` },
        { metric: 'Mentor Engagement', percentile: `${Math.round(percentiles.mentor_engagement || 0)}%` }
      ];

      const startY = finalY > 200 ? 40 : finalY + 14;

      if (finalY > 200) {
        doc.setFontSize(16);
        doc.setTextColor(40, 40, 40);
        doc.text('Comparative Analytics', 14, 22);
        doc.setFontSize(14);
        doc.text('Percentile Rankings', 14, 32);
      }

      autoTable(doc, {
        startY: startY,
        head: [['Metric', 'Percentile Rank']],
        body: percentileData.map(row => [row.metric, row.percentile]),
        theme: 'striped',
        headStyles: {
          fillColor: [75, 58, 176],
          textColor: [255, 255, 255],
          fontStyle: 'bold'
        },
        styles: {
          fontSize: 10,
          cellPadding: 3
        }
      });
    }
  }

  // Add recommended actions section if available
  if (analyticsData.recommended_actions && analyticsData.recommended_actions.length > 0) {
    // Check if we need a new page
    const finalY = (doc as any).lastAutoTable.finalY || 200;
    if (finalY > 200) {
      doc.addPage();
    } else {
      doc.setFontSize(16);
      doc.setTextColor(40, 40, 40);
      doc.text('Recommended Actions', 14, finalY + 10);
    }

    const startY = finalY > 200 ? 40 : finalY + 14;

    if (finalY > 200) {
      doc.setFontSize(16);
      doc.setTextColor(40, 40, 40);
      doc.text('Recommended Actions', 14, 22);
    }

    // Create recommended actions table
    const actionsData = analyticsData.recommended_actions.map((action: any) => ({
      title: action.title,
      priority: action.priority,
      description: action.description
    }));

    autoTable(doc, {
      startY: startY,
      head: [['Action', 'Priority', 'Description']],
      body: actionsData.map(row => [row.title, row.priority.toUpperCase(), row.description]),
      theme: 'striped',
      headStyles: {
        fillColor: [75, 58, 176],
        textColor: [255, 255, 255],
        fontStyle: 'bold'
      },
      styles: {
        fontSize: 10,
        cellPadding: 3
      }
    });
  }

  // Save the PDF
  doc.save(`${filename || 'analytics_report'}.pdf`);
};

/**
 * Export business plan to PDF with proper formatting
 * @param businessPlan Business plan object
 * @param sections Array of business plan sections
 * @param filename Optional filename
 */
export const exportBusinessPlanToPDF = async (
  businessPlan: any,
  sections: any[],
  filename?: string
) => {
  if (!businessPlan) {
    console.error('No business plan data to export');
    alert('No business plan data available to export.');
    return;
  }

  try {
    // Prepare comprehensive data for PDF
    const title = businessPlan.title || 'Business Plan';
    const subtitle = businessPlan.business_idea_title
      ? `Business Plan for ${businessPlan.business_idea_title}`
      : 'Business Plan Document';

    // Create detailed content for PDF
    const content = sections && sections.length > 0
      ? sections.map(section => ({
          title: section.title || 'Untitled Section',
          content: section.content || 'No content available',
          isCompleted: section.is_completed || false,
          order: section.order || 0
        }))
      : [{
          title: 'Business Plan Structure',
          content: 'No sections have been created yet.',
          isCompleted: false,
          order: 0
        }];

    // Sort sections by order
    content.sort((a, b) => a.order - b.order);

    // Enhanced PDF data with better formatting
    const columns = [
      { header: 'Section', dataKey: 'section' },
      { header: 'Content', dataKey: 'content' },
      { header: 'Status', dataKey: 'status' }
    ];

    const data = content.map(item => ({
      section: item.title,
      content: item.content
        ? item.content.replace(/<[^>]*>/g, ' ').trim().substring(0, 200) + (item.content.length > 200 ? '...' : '')
        : 'No content available',
      status: item.isCompleted ? 'Completed' : 'In Progress'
    }));

    // Add metadata row
    const metadataRow = {
      section: 'Business Plan Information',
      content: `Title: ${title}\nCreated: ${new Date().toLocaleDateString()}\nCompletion: ${businessPlan.completion_percentage || 0}%`,
      status: 'Metadata'
    };

    const finalData = [metadataRow, ...data];

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0];
    const finalFilename = filename || `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_business_plan_${timestamp}`;

    // Call the generic exportToPDF function
    exportToPDF(
      columns,
      finalData,
      title,
      subtitle,
      finalFilename
    );

    // Track the export
    if (businessPlan.id) {
      try {
        const { businessPlanAnalyticsAPI } = await import('../services/businessPlanAnalyticsApi');
        await businessPlanAnalyticsAPI.trackExport({
          business_plan_id: businessPlan.id,
          export_format: 'pdf',
          export_type: 'full',
          sections_included: sections.map(s => s.id).filter(Boolean),
          export_successful: true
        });
      } catch (error) {
        console.warn('Failed to track export:', error);
      }
    }

  } catch (error) {
    console.error('Export failed:', error);
    alert('Failed to export business plan. Please try again.');

    // Track failed export
    if (businessPlan.id) {
      try {
        const { businessPlanAnalyticsAPI } = await import('../services/businessPlanAnalyticsApi');
        await businessPlanAnalyticsAPI.trackExport({
          business_plan_id: businessPlan.id,
          export_format: 'pdf',
          export_type: 'full',
          export_successful: false,
          error_message: error instanceof Error ? error.message : 'Unknown error'
        });
      } catch (trackError) {
        console.warn('Failed to track failed export:', trackError);
      }
    }
  }
};

/**
 * Export business plan to Word document
 * @param businessPlan Business plan object
 * @param sections Array of business plan sections
 * @param filename Optional filename
 */
export const exportBusinessPlanToWord = async (
  businessPlan: any,
  sections: any[],
  filename?: string
) => {
  if (!businessPlan) {
    console.error('No business plan data to export');
    alert('No business plan data available to export.');
    return;
  }

  try {
    // Create Word document content
    const title = businessPlan.title || 'Business Plan';
    const content = sections && sections.length > 0
      ? sections.map(section => ({
          title: section.title || 'Untitled Section',
          content: section.content || 'No content available',
          order: section.order || 0
        }))
      : [{
          title: 'Business Plan Structure',
          content: 'No sections have been created yet.',
          order: 0
        }];

    // Sort sections by order
    content.sort((a, b) => a.order - b.order);

    // Generate Word document content
    let wordContent = `${title}\n\n`;
    if (businessPlan.business_idea_title) {
      wordContent += `Business Idea: ${businessPlan.business_idea_title}\n\n`;
    }
    wordContent += `Generated on: ${new Date().toLocaleDateString()}\n`;
    wordContent += `Completion: ${businessPlan.completion_percentage || 0}%\n\n`;
    wordContent += '=' .repeat(50) + '\n\n';

    content.forEach((section, index) => {
      wordContent += `${index + 1}. ${section.title}\n`;
      wordContent += '-'.repeat(section.title.length + 4) + '\n';
      wordContent += section.content.replace(/<[^>]*>/g, '').trim() + '\n\n';
    });

    // Create and download the file
    const blob = new Blob([wordContent], { type: 'application/msword' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;

    const timestamp = new Date().toISOString().split('T')[0];
    const finalFilename = filename || `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_business_plan_${timestamp}`;
    a.download = `${finalFilename}.doc`;

    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Track the export
    if (businessPlan.id) {
      try {
        const { businessPlanAnalyticsAPI } = await import('../services/businessPlanAnalyticsApi');
        await businessPlanAnalyticsAPI.trackExport({
          business_plan_id: businessPlan.id,
          export_format: 'word',
          export_type: 'full',
          sections_included: sections.map(s => s.id).filter(Boolean),
          export_successful: true
        });
      } catch (error) {
        console.warn('Failed to track export:', error);
      }
    }

  } catch (error) {
    console.error('Word export failed:', error);
    alert('Failed to export business plan to Word. Please try again.');

    // Track failed export
    if (businessPlan.id) {
      try {
        const { businessPlanAnalyticsAPI } = await import('../services/businessPlanAnalyticsApi');
        await businessPlanAnalyticsAPI.trackExport({
          business_plan_id: businessPlan.id,
          export_format: 'word',
          export_type: 'full',
          export_successful: false,
          error_message: error instanceof Error ? error.message : 'Unknown error'
        });
      } catch (trackError) {
        console.warn('Failed to track failed export:', trackError);
      }
    }
  }
};

/**
 * Format a number with commas for thousands
 *
 * @param num Number to format
 * @returns Formatted number string
 */
export const formatNumber = (num: number): string => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

/**
 * Format a percentage value
 *
 * @param value Percentage value
 * @param decimals Number of decimal places
 * @returns Formatted percentage string
 */
export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${value.toFixed(decimals)}%`;
};
