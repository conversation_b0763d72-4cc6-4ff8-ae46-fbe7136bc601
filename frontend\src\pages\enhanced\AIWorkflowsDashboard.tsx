/**
 * AI Workflows Dashboard
 * Central hub for all AI-powered workflows and automation
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Brain,
  Zap,
  FileText,
  Search,
  Users,
  Lightbulb,
  TrendingUp,
  Target,
  Clock,
  CheckCircle,
  ArrowRight,
  Play,
  Pause,
  Settings,
  BarChart3,
  Award,
  Sparkles
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
// MainLayout removed - handled by routing system
// Placeholder components - these would be implemented separately
const AutomatedBusinessPlanGenerator = ({ businessIdeaId, businessIdeaData, userId, className }: any) => (
  <div className={`p-6 bg-gray-100 dark:bg-gray-800 rounded-lg ${className}`}>
    <h3 className="text-lg font-semibold mb-4">Automated Business Plan Generator</h3>
    <p>Generating business plan for idea {businessIdeaId}...</p>
  </div>
);

const AIMarketResearchEngine = ({ businessIdeaId, businessIdeaData, userId, className }: any) => (
  <div className={`p-6 bg-gray-100 dark:bg-gray-800 rounded-lg ${className}`}>
    <h3 className="text-lg font-semibold mb-4">AI Market Research Engine</h3>
    <p>Researching market for idea {businessIdeaId}...</p>
  </div>
);

const SmartMentorshipMatcher = ({ businessIdeaId, businessIdeaData, userId, className }: any) => (
  <div className={`p-6 bg-gray-100 dark:bg-gray-800 rounded-lg ${className}`}>
    <h3 className="text-lg font-semibold mb-4">Smart Mentorship Matcher</h3>
    <p>Finding mentors for idea {businessIdeaId}...</p>
  </div>
);

const IntelligentResourceRecommendations = ({ businessIdeaId, businessIdeaData, userId, className }: any) => (
  <div className={`p-6 bg-gray-100 dark:bg-gray-800 rounded-lg ${className}`}>
    <h3 className="text-lg font-semibold mb-4">Intelligent Resource Recommendations</h3>
    <p>Recommending resources for idea {businessIdeaId}...</p>
  </div>
);

const ProactiveAINotifications = ({ userId }: any) => (
  <div className="flex items-center space-x-2 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
    <span className="text-sm text-blue-700 dark:text-blue-300">AI Active</span>
  </div>
);

import { useAppSelector } from '../../store/hooks';
import { useAIContext } from '../../contexts/AIContextProvider';

interface WorkflowStatus {
  id: string;
  name: string;
  status: 'idle' | 'running' | 'completed' | 'error';
  progress: number;
  lastRun: Date | null;
  nextRun: Date | null;
  results?: any;
}

const AIWorkflowsDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.auth);
  const { aiData, updateContext } = useAIContext();

  const [activeWorkflow, setActiveWorkflow] = useState<string>('overview');
  const [selectedBusinessIdea, setSelectedBusinessIdea] = useState<any>(null);
  const [workflowStatuses, setWorkflowStatuses] = useState<WorkflowStatus[]>([
    {
      id: 'business_plan',
      name: 'Business Plan Generation',
      status: 'idle',
      progress: 0,
      lastRun: null,
      nextRun: null
    },
    {
      id: 'market_research',
      name: 'Market Research',
      status: 'idle',
      progress: 0,
      lastRun: null,
      nextRun: null
    },
    {
      id: 'mentor_matching',
      name: 'Mentor Matching',
      status: 'idle',
      progress: 0,
      lastRun: null,
      nextRun: null
    },
    {
      id: 'resource_recommendations',
      name: 'Resource Recommendations',
      status: 'completed',
      progress: 100,
      lastRun: new Date(Date.now() - 3600000), // 1 hour ago
      nextRun: null
    }
  ]);

  // Mock business ideas for demo
  const businessIdeas = [
    {
      id: 1,
      title: t("common.aipowered.learning.platform", "AI-Powered Learning Platform"),
      description: 'Personalized learning platform using AI to adapt to individual learning styles',
      industry: 'Education Technology',
      stage: 'idea'
    },
    {
      id: 2,
      title: 'Sustainable Food Delivery',
      description: 'Eco-friendly food delivery service with zero-waste packaging',
      industry: 'Food & Beverage',
      stage: 'validation'
    }
  ];

  useEffect(() => {
    updateContext({
      currentPage: 'ai-workflows-dashboard',
      currentContext: {
        pageType: 'ai_workflows',
        activeWorkflow,
        selectedBusinessIdea
      }
    });
  }, [activeWorkflow, selectedBusinessIdea, updateContext]);

  const workflowComponents = {
    business_plan: AutomatedBusinessPlanGenerator,
    market_research: AIMarketResearchEngine,
    mentor_matching: SmartMentorshipMatcher,
    resource_recommendations: IntelligentResourceRecommendations
  };

  const workflowConfigs = [
    {
      id: 'business_plan',
      name: t('ai.workflows.businessPlan', 'Business Plan Generator'),
      description: t('ai.workflows.businessPlanDesc', 'Generate comprehensive business plans automatically'),
      icon: FileText,
      color: 'blue',
      features: [
        t('ai.executive.summary.company', 'Executive Summary'),
        t('ai.market.analysis', 'Market Analysis'),
        t('ai.financial.projections', 'Financial Projections'),
        t('ai.riskAssessment', 'Risk Assessment')
      ]
    },
    {
      id: 'market_research',
      name: t('ai.workflows.marketResearch', 'Market Research Engine'),
      description: t('ai.workflows.marketResearchDesc', 'Automated market analysis and competitor research'),
      icon: Search,
      color: 'green',
      features: [
        t('ai.marketSizing', 'Market Sizing'),
        t('ai.competitive.analysis', 'Competitor Analysis'),
        t('ai.trendIdentification', 'Trend Identification'),
        t('ai.opportunityMapping', 'Opportunity Mapping')
      ]
    },
    {
      id: 'mentor_matching',
      name: 'Smart Mentorship Matcher',
      description: 'AI-powered mentor matching based on compatibility',
      icon: Users,
      color: 'purple',
      features: ['Compatibility Scoring', 'Experience Matching', 'Availability Tracking', 'Success Prediction']
    },
    {
      id: 'resource_recommendations',
      name: 'Resource Recommendations',
      description: 'Intelligent recommendations for tools and learning materials',
      icon: Lightbulb,
      color: 'orange',
      features: ['Personalized Suggestions', 'Relevance Scoring', 'Cost Analysis', 'Learning Paths']
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      case 'completed':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'error':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getWorkflowColor = (color: string) => {
    const colors = {
      blue: 'from-blue-500 to-blue-600',
      green: 'from-green-500 to-green-600',
      purple: 'from-purple-500 to-purple-600',
      orange: 'from-orange-500 to-orange-600'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const renderWorkflowComponent = () => {
    if (activeWorkflow === 'overview') return null;

    const WorkflowComponent = workflowComponents[activeWorkflow as keyof typeof workflowComponents];
    if (!WorkflowComponent || !selectedBusinessIdea) return null;

    return (
      <WorkflowComponent
        businessIdeaId={selectedBusinessIdea.id}
        businessIdeaData={selectedBusinessIdea}
        userId={user?.id}
        className="mt-6"
      />
    );
  };

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {t('ai.workflows.title', 'AI Workflows Dashboard')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t('ai.workflows.subtitle', 'Automated AI-powered workflows for business development')}
            </p>
          </div>

          <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            <ProactiveAINotifications userId={user?.id} />
            <div className={`flex items-center space-x-2 px-3 py-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Brain className="w-4 h-4 text-green-500" />
              <span className="text-sm text-green-700 dark:text-green-300">
                {t('ai.workflows.active', 'AI Workflows Active')}
              </span>
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            </div>
          </div>
        </div>

        {/* Business Idea Selection */}
        {activeWorkflow !== 'overview' && (
          <div className="glass-light border rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
              {t('ai.workflows.selectIdea', 'Select Business Idea')}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {businessIdeas.map((idea) => (
                <button
                  key={idea.id}
                  onClick={() => setSelectedBusinessIdea(idea)}
                  className={`text-left p-3 rounded-lg border transition-all duration-200 ${
                    selectedBusinessIdea?.id === idea.id
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-600'
                  }`}
                >
                  <h4 className="font-medium text-gray-900 dark:text-white">{idea.title}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{idea.description}</p>
                  <div className={`flex items-center space-x-2 mt-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full">
                      {idea.industry}
                    </span>
                    <span className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full">
                      {idea.stage}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Workflow Navigation */}
        <div className={`flex space-x-2 overflow-x-auto pb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={() => setActiveWorkflow('overview')}
            className={`px-4 py-2 text-sm font-medium rounded-lg whitespace-nowrap transition-colors ${
              activeWorkflow === 'overview'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
            }`}
          >
            {t('ai.workflows.overview', 'Overview')}
          </button>
          {workflowConfigs.map((workflow) => (
            <button
              key={workflow.id}
              onClick={() => setActiveWorkflow(workflow.id)}
              className={`px-4 py-2 text-sm font-medium rounded-lg whitespace-nowrap transition-colors ${
                activeWorkflow === workflow.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
              }`}
            >
              {workflow.name}
            </button>
          ))}
        </div>

        {/* Overview Content */}
        {activeWorkflow === 'overview' && (
          <div className="space-y-8">
            {/* Workflow Status Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {workflowStatuses.map((workflow) => (
                <div
                  key={workflow.id}
                  className="glass-light border rounded-lg p-4"
                >
                  <div className={`flex items-center justify-between mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                      {workflow.name}
                    </h4>
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(workflow.status)}`}>
                      {workflow.status}
                    </span>
                  </div>

                  {workflow.status === 'running' && (
                    <div className="mb-3">
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${workflow.progress}%` }}
                        />
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {workflow.progress}% complete
                      </span>
                    </div>
                  )}

                  {workflow.lastRun && (
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Last run: {workflow.lastRun.toLocaleTimeString()}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Available Workflows */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                {t('ai.workflows.available', 'Available AI Workflows')}
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {workflowConfigs.map((workflow) => {
                  const IconComponent = workflow.icon;
                  return (
                    <div
                      key={workflow.id}
                      className="glass-light border rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200 cursor-pointer"
                      onClick={() => setActiveWorkflow(workflow.id)}
                    >
                      <div className={`h-2 bg-gradient-to-r ${getWorkflowColor(workflow.color)}`} />

                      <div className="p-6">
                        <div className={`flex items-start space-x-4 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <div className={`p-3 rounded-lg bg-gradient-to-r ${getWorkflowColor(workflow.color)}`}>
                            <IconComponent className="w-6 h-6 text-white" />
                          </div>
                          <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                            <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                              {workflow.name}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {workflow.description}
                            </p>
                          </div>
                          <ArrowRight className="w-5 h-5 text-gray-400" />
                        </div>

                        <div className="space-y-2">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                            {t('ai.workflows.features', 'Key Features:')}
                          </h4>
                          <ul className="space-y-1">
                            {workflow.features.map((feature, index) => (
                              <li key={index} className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                                <CheckCircle className="w-3 h-3 text-green-500" />
                                <span className="text-xs text-gray-600 dark:text-gray-400">{feature}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                          <button className={`w-full flex items-center justify-center space-x-2 py-2 px-4 bg-gradient-to-r ${getWorkflowColor(workflow.color)} text-white rounded-lg hover:opacity-90 transition-opacity`}>
                            <Play className="w-4 h-4" />
                            <span className="text-sm font-medium">
                              {t('ai.workflows.launch', 'Launch Workflow')}
                            </span>
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Quick Stats */}
            <div className="glass-light border rounded-lg p-6">
              <div className={`flex items-center space-x-3 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                <Sparkles className="w-6 h-6 text-blue-500" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {t('ai.workflows.stats', 'AI Workflow Statistics')}
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">12</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('ai.workflows.totalRuns', 'Total Workflow Runs')}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">8</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('ai.workflows.successful', 'Successful Completions')}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">2.5h</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('ai.workflows.timeSaved', 'Time Saved')}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">95%</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('ai.workflows.accuracy', 'Average Accuracy')}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Active Workflow Component */}
        {renderWorkflowComponent()}
      </div>
              </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default AIWorkflowsDashboard;
