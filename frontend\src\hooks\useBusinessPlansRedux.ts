import { useCallback, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import {
  fetchBusinessPlans,
  fetchBusinessPlan,
  createBusinessPlan,
  updateBusinessPlan,
  deleteBusinessPlan,
  fetchBusinessPlanSections,
  updateBusinessPlanSection,
  autoSaveSection,
  setActiveSectionId,
  setSectionContent,
  setHasUnsavedChanges,
  setShowUnsavedWarning,
  setIsGeneratingContent,
  clearAutoSaveError,
  setFilters,
  clearFilters,
  clearError,
  clearSectionsError,
  resetBusinessPlanState,
  selectBusinessPlans,
  selectCurrentBusinessPlan,
  selectBusinessPlanSections,
  selectCurrentSection,
  selectActiveSectionId,
  selectSectionContent,
  selectHasUnsavedChanges,
  selectIsLoading,
  selectIsAutoSaving,
  selectAutoSaveError,
  selectIsGeneratingContent,
} from '../store/businessPlansSlice';
import { BusinessPlan } from '../services/businessPlanApi';

/**
 * Hook for managing business plans list
 */
export const useBusinessPlansList = () => {
  const dispatch = useAppDispatch();
  const businessPlans = useAppSelector(selectBusinessPlans);
  const isLoading = useAppSelector(state => state.businessPlans.isLoading);
  const error = useAppSelector(state => state.businessPlans.error);
  const filters = useAppSelector(state => state.businessPlans.filters);

  const fetchPlans = useCallback((businessIdeaId?: number) => {
    dispatch(fetchBusinessPlans({ businessIdeaId, filters }));
  }, [dispatch, filters]);

  const setBusinessPlanFilters = useCallback((newFilters: any) => {
    dispatch(setFilters(newFilters));
  }, [dispatch]);

  const clearBusinessPlanFilters = useCallback(() => {
    dispatch(clearFilters());
  }, [dispatch]);

  const clearBusinessPlanError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  return {
    businessPlans,
    isLoading,
    error,
    filters,
    fetchPlans,
    setFilters: setBusinessPlanFilters,
    clearFilters: clearBusinessPlanFilters,
    clearError: clearBusinessPlanError,
  };
};

/**
 * Hook for managing a single business plan
 */
export const useBusinessPlanDetail = (id?: number) => {
  const dispatch = useAppDispatch();
  const currentBusinessPlan = useAppSelector(selectCurrentBusinessPlan);
  const isLoading = useAppSelector(selectIsLoading);
  const error = useAppSelector(state => state.businessPlans.error);

  const fetchPlan = useCallback((planId: number) => {
    dispatch(fetchBusinessPlan(planId));
  }, [dispatch]);

  const updatePlan = useCallback((planId: number, data: Partial<BusinessPlan>) => {
    return dispatch(updateBusinessPlan({ id: planId, data }));
  }, [dispatch]);

  const deletePlan = useCallback((planId: number) => {
    return dispatch(deleteBusinessPlan(planId));
  }, [dispatch]);

  // Auto-fetch when id changes
  useEffect(() => {
    if (id && (!currentBusinessPlan || currentBusinessPlan.id !== id)) {
      fetchPlan(id);
    }
  }, [id, currentBusinessPlan, fetchPlan]);

  return {
    businessPlan: currentBusinessPlan,
    isLoading,
    error,
    fetchPlan,
    updatePlan,
    deletePlan,
  };
};

/**
 * Hook for creating business plans
 */
export const useCreateBusinessPlan = () => {
  const dispatch = useAppDispatch();
  const isCreating = useAppSelector(state => state.businessPlans.isCreating);
  const error = useAppSelector(state => state.businessPlans.error);

  const createPlan = useCallback((data: Partial<BusinessPlan>) => {
    return dispatch(createBusinessPlan(data));
  }, [dispatch]);

  return {
    createPlan,
    isCreating,
    error,
  };
};

/**
 * Hook for managing business plan sections
 */
export const useBusinessPlanSections = (planId?: number) => {
  const dispatch = useAppDispatch();
  const sections = useAppSelector(selectBusinessPlanSections);
  const currentSection = useAppSelector(selectCurrentSection);
  const activeSectionId = useAppSelector(selectActiveSectionId);
  const isLoading = useAppSelector(state => state.businessPlans.isSectionsLoading);
  const error = useAppSelector(state => state.businessPlans.sectionsError);

  const fetchSections = useCallback((businessPlanId: number) => {
    dispatch(fetchBusinessPlanSections(businessPlanId));
  }, [dispatch]);

  const setActiveSection = useCallback((sectionId: number | null) => {
    dispatch(setActiveSectionId(sectionId));
  }, [dispatch]);

  const clearSectionError = useCallback(() => {
    dispatch(clearSectionsError());
  }, [dispatch]);

  // Auto-fetch sections when planId changes
  useEffect(() => {
    if (planId) {
      fetchSections(planId);
    }
  }, [planId, fetchSections]);

  return {
    sections,
    currentSection,
    activeSectionId,
    isLoading,
    error,
    fetchSections,
    setActiveSection,
    clearError: clearSectionError,
  };
};

/**
 * Hook for managing section content editing
 */
export const useBusinessPlanSectionEditor = () => {
  const dispatch = useAppDispatch();
  const sectionContent = useAppSelector(selectSectionContent);
  const hasUnsavedChanges = useAppSelector(selectHasUnsavedChanges);
  const isUpdating = useAppSelector(state => state.businessPlans.isSectionUpdating);
  const isAutoSaving = useAppSelector(selectIsAutoSaving);
  const autoSaveError = useAppSelector(selectAutoSaveError);
  const isGeneratingContent = useAppSelector(selectIsGeneratingContent);
  const showUnsavedWarning = useAppSelector(state => state.businessPlans.showUnsavedWarning);
  const lastSavedAt = useAppSelector(state => state.businessPlans.lastSavedAt);

  const updateContent = useCallback((content: string) => {
    dispatch(setSectionContent(content));
  }, [dispatch]);

  const saveSection = useCallback((sectionId: number, content: string, planId: number) => {
    return dispatch(updateBusinessPlanSection({ sectionId, content, planId }));
  }, [dispatch]);

  const autoSave = useCallback((sectionId: number, content: string, planId: number) => {
    return dispatch(autoSaveSection({ sectionId, content, planId }));
  }, [dispatch]);

  const setUnsavedChanges = useCallback((hasChanges: boolean) => {
    dispatch(setHasUnsavedChanges(hasChanges));
  }, [dispatch]);

  const setUnsavedWarning = useCallback((show: boolean) => {
    dispatch(setShowUnsavedWarning(show));
  }, [dispatch]);

  const setGeneratingContent = useCallback((isGenerating: boolean) => {
    dispatch(setIsGeneratingContent(isGenerating));
  }, [dispatch]);

  const clearAutoSaveErr = useCallback(() => {
    dispatch(clearAutoSaveError());
  }, [dispatch]);

  return {
    sectionContent,
    hasUnsavedChanges,
    isUpdating,
    isAutoSaving,
    autoSaveError,
    isGeneratingContent,
    showUnsavedWarning,
    lastSavedAt,
    updateContent,
    saveSection,
    autoSave,
    setUnsavedChanges,
    setUnsavedWarning,
    setGeneratingContent,
    clearAutoSaveError: clearAutoSaveErr,
  };
};

/**
 * Hook for auto-saving with debouncing
 */
export const useAutoSave = (
  sectionId: number | null,
  planId: number | null,
  delay: number = 10000 // 10 seconds
) => {
  const dispatch = useAppDispatch();
  const sectionContent = useAppSelector(selectSectionContent);
  const hasUnsavedChanges = useAppSelector(selectHasUnsavedChanges);
  const isAutoSaving = useAppSelector(selectIsAutoSaving);

  useEffect(() => {
    if (!sectionId || !planId || !hasUnsavedChanges || isAutoSaving) {
      return;
    }

    const timeoutId = setTimeout(() => {
      if (sectionContent.trim()) {
        dispatch(autoSaveSection({ sectionId, content: sectionContent, planId }));
      }
    }, delay);

    return () => clearTimeout(timeoutId);
  }, [dispatch, sectionId, planId, sectionContent, hasUnsavedChanges, isAutoSaving, delay]);
};

/**
 * Hook for business plan state management
 */
export const useBusinessPlanState = () => {
  const dispatch = useAppDispatch();
  const state = useAppSelector(state => state.businessPlans);

  const resetState = useCallback(() => {
    dispatch(resetBusinessPlanState());
  }, [dispatch]);

  const clearAllErrors = useCallback(() => {
    dispatch(clearError());
    dispatch(clearSectionsError());
    dispatch(clearAutoSaveError());
  }, [dispatch]);

  return {
    ...state,
    resetState,
    clearAllErrors,
  };
};

/**
 * Hook for business plan analytics and statistics
 */
export const useBusinessPlanAnalytics = () => {
  const businessPlans = useAppSelector(selectBusinessPlans);
  const sections = useAppSelector(selectBusinessPlanSections);
  const currentBusinessPlan = useAppSelector(selectCurrentBusinessPlan);

  const analytics = {
    totalPlans: businessPlans.length,
    completedPlans: businessPlans.filter(plan => plan.status === 'completed').length,
    draftPlans: businessPlans.filter(plan => plan.status === 'draft').length,
    inProgressPlans: businessPlans.filter(plan => plan.status === 'in_progress').length,
    
    // Current plan analytics
    currentPlanCompletion: currentBusinessPlan?.completion_percentage || 0,
    totalSections: sections.length,
    completedSections: sections.filter(section => 
      section.content && section.content.trim().length > 0
    ).length,
    
    // Content analytics
    totalWords: sections.reduce((total, section) => {
      const words = section.content 
        ? section.content.replace(/<[^>]*>/g, '').split(/\s+/).length 
        : 0;
      return total + words;
    }, 0),
    
    averageCompletion: businessPlans.length > 0 
      ? businessPlans.reduce((sum, plan) => sum + (plan.completion_percentage || 0), 0) / businessPlans.length
      : 0,
  };

  return analytics;
};

/**
 * Hook for keyboard shortcuts
 */
export const useBusinessPlanKeyboardShortcuts = (
  onSave?: () => void,
  onGenerateAI?: () => void,
  onClose?: () => void
) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+S or Cmd+S for save
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        onSave?.();
      }
      
      // Ctrl+G or Cmd+G for AI generation
      if ((event.ctrlKey || event.metaKey) && event.key === 'g') {
        event.preventDefault();
        onGenerateAI?.();
      }
      
      // Escape for close/cancel
      if (event.key === 'Escape') {
        event.preventDefault();
        onClose?.();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onSave, onGenerateAI, onClose]);
};