import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  FileText,
  Save,
  RefreshCw,
  Check,
  AlertCircle,
  ChevronDown,
  ChevronUp,
  ArrowLeft,
  Sparkles,
  Download,
  BarChart2,
  Eye
} from 'lucide-react';
import { useAppSelector } from '../../store/hooks';
import {
  BusinessPlan,
  BusinessPlanSection,
  businessPlansAPI,
  businessPlanSectionsAPI
} from '../../services/businessPlanApi';
import ModernRichTextEditor from './ModernRichTextEditor';
import './ModernRichTextEditor.css';
import './BusinessPlanEditor.css';
import { exportToPDF } from '../../utils/exportUtils';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { 
  validateBusinessPlanContent, 
  validateBusinessPlanTitle,
  defaultRateLimiter 
} from '../../utils/security';
import { 
  useBusinessPlanDetail,
  useBusinessPlanSections,
  useBusinessPlanSectionEditor,
  useAutoSave,
  useBusinessPlanKeyboardShortcuts
} from '../../hooks/useBusinessPlansRedux';
import BusinessPlanHeader from './BusinessPlanHeader';
import BusinessPlanSectionList from './BusinessPlanSectionList';
import BusinessPlanSectionEditor from './BusinessPlanSectionEditor';
import BusinessPlanErrorBoundary from './BusinessPlanErrorBoundary';

interface BusinessPlanEditorProps {
  businessPlanId?: number;
  onBack?: () => void;
}

const BusinessPlanEditorRedux: React.FC<BusinessPlanEditorProps> = ({ 
  businessPlanId: propBusinessPlanId,
  onBack
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { id: paramId } = useParams<{ id: string }>();
  const businessPlanId = propBusinessPlanId || (paramId ? parseInt(paramId) : undefined);

  const navigate = useNavigate();
  const { user } = useAppSelector(state => state.auth);

  // Redux hooks for business plan management
  const { businessPlan, isLoading: planLoading, error: planError, updatePlan } = useBusinessPlanDetail(businessPlanId);
  const { 
    sections, 
    currentSection, 
    activeSectionId, 
    isLoading: sectionsLoading, 
    error: sectionsError,
    setActiveSection 
  } = useBusinessPlanSections(businessPlanId);
  
  const {
    sectionContent,
    hasUnsavedChanges,
    isUpdating: isSaving,
    isAutoSaving,
    autoSaveError,
    isGeneratingContent,
    showUnsavedWarning,
    lastSavedAt,
    updateContent,
    saveSection,
    setUnsavedChanges,
    setUnsavedWarning,
    setGeneratingContent,
    clearAutoSaveError
  } = useBusinessPlanSectionEditor();

  // Local state for UI
  const [expandedSections, setExpandedSections] = useState<Record<number, boolean>>({});
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showFeedback, setShowFeedback] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [showAutoSaveIndicator, setShowAutoSaveIndicator] = useState(false);

  // Computed values
  const loading = planLoading || sectionsLoading;
  const error = planError || sectionsError;

  // Auto-save functionality
  useAutoSave(activeSectionId, businessPlanId, 10000);

  // Initialize expanded sections when sections are loaded
  useEffect(() => {
    if (sections.length > 0) {
      // Initialize expanded sections
      const expanded: Record<number, boolean> = {};
      sections.forEach(section => {
        expanded[section.id] = false;
      });
      setExpandedSections(expanded);

      // Set first section as active if no active section is set
      if (!activeSectionId) {
        const incompleteSections = sections.filter(s => !s.is_completed);
        if (incompleteSections.length > 0) {
          setActiveSection(incompleteSections[0].id);
        } else if (sections.length > 0) {
          setActiveSection(sections[0].id);
        }
      }
    }
  }, [sections, activeSectionId, setActiveSection]);

  // Keyboard shortcuts
  useBusinessPlanKeyboardShortcuts(
    () => handleSaveSection(),
    () => handleGenerateContent(),
    () => setUnsavedWarning(false)
  );

  // Handle section selection
  const handleSectionSelect = useCallback((sectionId: number) => {
    if (sectionId === activeSectionId) {
      // Toggle expanded state if clicking the active section
      setExpandedSections(prev => ({
        ...prev,
        [sectionId]: !prev[sectionId]
      }));
      return;
    }

    // Check for unsaved changes before switching sections
    if (hasUnsavedChanges) {
      setUnsavedWarning(true);
      return;
    }

    // Set the new active section
    setActiveSection(sectionId);
    
    // Expand the selected section
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: true
    }));
  }, [activeSectionId, hasUnsavedChanges, setActiveSection, setUnsavedWarning]);

  // Handle content change
  const handleContentChange = useCallback((content: string) => {
    updateContent(content);
    
    // Show auto-save indicator when changes are made
    if (showAutoSaveIndicator) {
      clearTimeout(window.autoSaveIndicatorTimeout);
    }
    
    // Auto-save is handled by the useAutoSave hook
  }, [updateContent, showAutoSaveIndicator]);

  // Handle manual save
  const handleSaveSection = useCallback(async () => {
    if (!activeSectionId || !businessPlanId) return;
    
    try {
      await saveSection(activeSectionId, sectionContent, businessPlanId);
      
      // Show success indicator
      setShowAutoSaveIndicator(true);
      window.autoSaveIndicatorTimeout = setTimeout(() => {
        setShowAutoSaveIndicator(false);
      }, 3000);
      
      setSuccess(t("businessPlan.sectionSaved"));
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error saving section:', error);
    }
  }, [activeSectionId, businessPlanId, sectionContent, saveSection, t]);

  // Handle AI content generation
  const handleGenerateContent = useCallback(async () => {
    if (!activeSectionId || !businessPlanId || !currentSection) return;
    
    setGeneratingContent(true);
    
    try {
      // Call AI API to generate content
      const generatedContent = await businessPlanSectionsAPI.generateContent(activeSectionId);
      
      // Update content with generated text
      updateContent(generatedContent.content);
      
      // Auto-save the generated content
      await saveSection(activeSectionId, generatedContent.content, businessPlanId);
      
      setSuccess(t("businessPlan.contentGenerated"));
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error generating content:', error);
    } finally {
      setGeneratingContent(false);
    }
  }, [activeSectionId, businessPlanId, currentSection, setGeneratingContent, updateContent, saveSection, t]);

  // Handle navigation back to business plans list
  const handleBack = useCallback(() => {
    if (hasUnsavedChanges) {
      setUnsavedWarning(true);
      return;
    }
    
    if (onBack) {
      onBack();
    } else {
      navigate('/dashboard/business-plans');
    }
  }, [hasUnsavedChanges, navigate, onBack, setUnsavedWarning]);

  // Handle export to PDF
  const handleExport = useCallback(async () => {
    if (!businessPlan) return;
    
    try {
      await exportToPDF(businessPlan, sections);
      setSuccess(t("businessPlan.exportSuccess"));
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error exporting to PDF:', error);
    }
  }, [businessPlan, sections, t]);

  // Handle analytics view
  const handleViewAnalytics = useCallback(() => {
    if (!businessPlanId) return;
    navigate(`/dashboard/business-plans/${businessPlanId}/analytics`);
  }, [businessPlanId, navigate]);

  // Handle preview
  const handlePreview = useCallback(() => {
    if (!businessPlanId) return;
    navigate(`/dashboard/business-plans/${businessPlanId}/preview`);
  }, [businessPlanId, navigate]);

  // Handle unsaved changes confirmation
  const handleConfirmSectionChange = useCallback((newSectionId: number) => {
    setUnsavedWarning(false);
    setActiveSection(newSectionId);
  }, [setActiveSection, setUnsavedWarning]);

  // Handle unsaved changes discard
  const handleDiscardChanges = useCallback(() => {
    setUnsavedWarning(false);
    setUnsavedChanges(false);
    
    // Reset content to the saved version
    if (currentSection) {
      updateContent(currentSection.content || '');
    }
  }, [currentSection, setUnsavedChanges, setUnsavedWarning, updateContent]);

  // Render loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw size={40} className="mx-auto mb-4 animate-spin text-purple-500" />
          <h2 className="text-xl font-semibold mb-2">{t("businessPlan.loading")}</h2>
          <p className="text-gray-500">{t("businessPlan.loadingDescription")}</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md">
          <AlertCircle size={40} className="mx-auto mb-4 text-red-500" />
          <h2 className="text-xl font-semibold mb-2">{t("businessPlan.error")}</h2>
          <p className="text-gray-500 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
          >
            {t("businessPlan.tryAgain")}
          </button>
        </div>
      </div>
    );
  }

  // Render main content
  return (
    <BusinessPlanErrorBoundary>
      <div className="business-plan-editor-container p-6 max-w-7xl mx-auto">
        {/* Success message */}
        {success && (
          <div className="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-md z-50 animate-fade-in-out">
            {success}
          </div>
        )}

        {/* Unsaved changes warning */}
        {showUnsavedWarning && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full">
              <h3 className="text-xl font-semibold mb-4 text-white">
                {t("businessPlan.unsavedChangesTitle")}
              </h3>
              <p className="mb-6 text-gray-300">
                {t("businessPlan.unsavedChangesDescription")}
              </p>
              <div className="flex justify-end space-x-4">
                <button
                  onClick={handleDiscardChanges}
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                >
                  {t("businessPlan.discardChanges")}
                </button>
                <button
                  onClick={handleSaveSection}
                  className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                >
                  {t("businessPlan.saveChanges")}
                </button>
              </div>
            </div>
          </div>
        )}

        {businessPlan && (
          <>
            {/* Business Plan Header */}
            <BusinessPlanHeader
              businessPlan={businessPlan}
              onBack={handleBack}
              onSave={handleSaveSection}
              onExport={handleExport}
              onViewAnalytics={handleViewAnalytics}
              onPreview={handlePreview}
              isSaving={isSaving}
              hasUnsavedChanges={hasUnsavedChanges}
              completionPercentage={businessPlan.completion_percentage || 0}
            />

            {/* Business Plan Sections and Editor */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* Sections List */}
              <div className="lg:col-span-1">
                <BusinessPlanSectionList
                  sections={sections}
                  activeSectionId={activeSectionId}
                  onSectionSelect={handleSectionSelect}
                  completedSections={sections.filter(s => s.is_completed).map(s => s.id)}
                  hasUnsavedChanges={hasUnsavedChanges}
                />
              </div>

              {/* Section Editor */}
              <div className="lg:col-span-3">
                <BusinessPlanSectionEditor
                  section={currentSection}
                  content={sectionContent}
                  onContentChange={handleContentChange}
                  onGenerateWithAI={handleGenerateContent}
                  isGeneratingContent={isGeneratingContent}
                  isSaving={isSaving}
                  showAutoSaveIndicator={showAutoSaveIndicator}
                  autoSaveError={autoSaveError}
                />
              </div>
            </div>
          </>
        )}
      </div>
    </BusinessPlanErrorBoundary>
  );
};

export default BusinessPlanEditorRedux;