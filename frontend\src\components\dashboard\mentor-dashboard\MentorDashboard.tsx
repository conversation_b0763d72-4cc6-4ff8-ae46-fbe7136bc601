import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../ui/card';
import Button from '../../ui/Button';
import { Badge } from '../../ui/badge';
import { Calendar, Users, Star, Clock, TrendingUp, MessageSquare, Video, Plus, ArrowRight, Award } from 'lucide-react';
import { Link } from 'react-router-dom';
import { apiClient } from '../../../services/apiClient';
import { formatDate as formatDateUtil } from '../../../utils/dateTimeFormatter';

interface MentorStats {
  totalMentees: number;
  activeMentees: number;
  upcomingSessions: number;
  completedSessions: number;
  averageRating: number;
  responseRate: number;
  monthlyEarnings: number;
}

interface UpcomingSession {
  id: string;
  menteeName: string;
  topic: string;
  date: string;
  time: string;
  duration: number;
  type: 'video' | 'phone' | 'in-person';
  status: 'confirmed' | 'pending' | 'rescheduled';
}

interface RecentActivity {
  id: string;
  type: 'session_completed' | 'new_mentee' | 'rating_received' | 'message_received';
  title: string;
  description: string;
  timestamp: string;
  mentee?: string;
  rating?: number;
}

interface QuickAction {
  title: string;
  description: string;
  icon: React.ReactNode;
  link: string;
  count?: number;
  highlight?: boolean;
}

const MentorDashboard: React.FC = () => {
  const [stats, setStats] = useState<MentorStats | null>(null);
  const [upcomingSessions, setUpcomingSessions] = useState<UpcomingSession[]>([]);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch real data from API
  useEffect(() => {
    const fetchMentorData = async () => {
      try {
        setLoading(true);

        // Fetch mentor dashboard stats
        const response = await apiClient.get('/api/roles/mentor/dashboard-stats/');
        const mentorStats: MentorStats = {
          totalMentees: response.data.totalMentees || 0,
          activeMentees: response.data.activeMentees || 0,
          upcomingSessions: response.data.upcomingSessions || 0,
          completedSessions: response.data.completedSessions || 0,
          averageRating: response.data.averageRating || 0,
          responseRate: response.data.responseRate || 0,
          monthlyEarnings: response.data.monthlyEarnings || 0
        };

        // Fetch upcoming sessions
        const sessionsResponse = await apiClient.get('/api/roles/mentor/upcoming-sessions/');
        const upcomingSessionsData: UpcomingSession[] = sessionsResponse.data.map((session: any) => ({
          id: session.id.toString(),
          menteeName: session.mentee_name || 'Unknown Mentee',
          topic: session.topic || 'General Mentoring',
          date: session.scheduled_date?.split('T')[0] || new Date().toISOString().split('T')[0],
          time: session.scheduled_time || '00:00',
          duration: session.duration || 60,
          type: session.session_type || 'video',
          status: session.status || 'pending'
        }));

    const mockActivity: RecentActivity[] = [
      {
        id: '1',
        type: 'rating_received',
        title: 'New 5-star rating',
        description: 'Alex Chen rated your last session',
        timestamp: '2024-01-16T15:30:00Z',
        mentee: 'Alex Chen',
        rating: 5
      },
      {
        id: '2',
        type: 'session_completed',
        title: 'Session completed',
        description: 'Fundraising strategy session with Sarah Johnson',
        timestamp: '2024-01-16T14:00:00Z',
        mentee: 'Sarah Johnson'
      },
      {
        id: '3',
        type: 'new_mentee',
        title: 'New mentee request',
        description: 'Emily Davis requested mentorship',
        timestamp: '2024-01-16T10:15:00Z',
        mentee: 'Emily Davis'
      },
      {
        id: '4',
        type: 'message_received',
        title: 'New message',
        description: 'Michael Rodriguez sent you a message',
        timestamp: '2024-01-16T09:30:00Z',
        mentee: 'Michael Rodriguez'
      }
        ];

        // Update state with real data
        setStats(mentorStats);
        setUpcomingSessions(upcomingSessionsData);
        setRecentActivity(mockActivity); // Keep mock activity for now as it has more complex structure

      } catch (error) {
        console.error('Error fetching mentor dashboard data:', error);

        // Fallback to empty data on error
        setStats({
          totalMentees: 0,
          activeMentees: 0,
          upcomingSessions: 0,
          completedSessions: 0,
          averageRating: 0,
          responseRate: 0,
          monthlyEarnings: 0
        });
        setUpcomingSessions([]);
        setRecentActivity([]);
      } finally {
        setLoading(false);
      }
    };

    fetchMentorData();
  }, []);

  const quickActions: QuickAction[] = [
    {
      title: 'Schedule Session',
      description: 'Book a new mentoring session',
      icon: <Plus className="w-6 h-6" />,
      link: '/dashboard/mentorship/sessions',
      highlight: true
    },
    {
      title: 'Manage Mentees',
      description: 'View and manage your mentees',
      icon: <Users className="w-6 h-6" />,
      link: '/dashboard/mentorship/mentees',
      count: stats?.activeMentees
    },
    {
      title: 'View Analytics',
      description: 'Track your mentoring performance',
      icon: <TrendingUp className="w-6 h-6" />,
      link: '/dashboard/mentorship/analytics'
    },
    {
      title: 'Update Availability',
      description: 'Manage your schedule',
      icon: <Calendar className="w-6 h-6" />,
      link: '/dashboard/mentorship/availability'
    },
    {
      title: 'Profile Settings',
      description: 'Update your mentor profile',
      icon: <Star className="w-6 h-6" />,
      link: '/dashboard/mentorship/profile'
    },
    {
      title: 'Messages',
      description: 'Chat with your mentees',
      icon: <MessageSquare className="w-6 h-6" />,
      link: '/dashboard/messages'
    }
  ];

  const getSessionTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="w-4 h-4 text-blue-600" />;
      case 'phone': return <Clock className="w-4 h-4 text-green-600" />;
      case 'in-person': return <Users className="w-4 h-4 text-purple-600" />;
      default: return <Calendar className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rescheduled': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'session_completed': return <Calendar className="w-4 h-4 text-green-600" />;
      case 'new_mentee': return <Users className="w-4 h-4 text-blue-600" />;
      case 'rating_received': return <Star className="w-4 h-4 text-yellow-600" />;
      case 'message_received': return <MessageSquare className="w-4 h-4 text-purple-600" />;
      default: return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  // Use centralized date formatting utility
  const formatDateForDisplay = (dateString: string) => {
    return formatDateUtil(dateString, {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    return new Date(`2024-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Mentor Dashboard</h1>
          <p className="text-gray-600 mt-1">Guide and support the next generation of entrepreneurs</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <MessageSquare className="w-4 h-4 mr-2" />
            Messages
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            Schedule Session
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Mentees</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.activeMentees}</p>
                  <p className="text-sm text-gray-600">of {stats.totalMentees} total</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Upcoming Sessions</p>
                  <p className="text-2xl font-bold text-green-600">{stats.upcomingSessions}</p>
                  <p className="text-sm text-green-600">This week</p>
                </div>
                <Calendar className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Average Rating</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.averageRating}</p>
                  <p className="text-sm text-yellow-600">★★★★★</p>
                </div>
                <Star className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Monthly Earnings</p>
                  <p className="text-2xl font-bold text-purple-600">${stats.monthlyEarnings.toLocaleString()}</p>
                  <p className="text-sm text-purple-600">+15% from last month</p>
                </div>
                <Award className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {quickActions.map((action, index) => (
                  <Link key={index} to={action.link}>
                    <div className={`p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer ${
                      action.highlight ? 'border-blue-300 bg-blue-50' : ''
                    }`}>
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg ${
                            action.highlight ? 'bg-blue-200' : 'bg-gray-100'
                          }`}>
                            {action.icon}
                          </div>
                          <div>
                            <h3 className="font-semibold">{action.title}</h3>
                            <p className="text-sm text-gray-600">{action.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {action.count !== undefined && (
                            <Badge className="bg-blue-100 text-blue-800">
                              {action.count}
                            </Badge>
                          )}
                          <ArrowRight className="w-4 h-4 text-gray-400" />
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3 p-3 border rounded">
                    {getActivityIcon(activity.type)}
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-sm">{activity.title}</h4>
                      <p className="text-xs text-gray-600 mb-2">{activity.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">{formatTimeAgo(activity.timestamp)}</span>
                        {activity.rating && (
                          <div className="flex items-center gap-1">
                            {Array.from({ length: activity.rating }, (_, i) => (
                              <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Link to="/dashboard/mentorship/sessions">
                  <Button variant="outline" className="w-full">
                    View All Activity
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Upcoming Sessions */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Upcoming Sessions</CardTitle>
            <Link to="/dashboard/mentorship/sessions">
              <Button variant="outline" size="sm">
                View All
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {upcomingSessions.map((session) => (
              <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  {getSessionTypeIcon(session.type)}
                  <div>
                    <h3 className="font-semibold">{session.topic}</h3>
                    <p className="text-sm text-gray-600">with {session.menteeName}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <p className="text-sm font-medium">{formatDateForDisplay(session.date)}</p>
                    <p className="text-sm text-gray-600">{formatTime(session.time)} ({session.duration}min)</p>
                  </div>
                  <Badge className={getStatusColor(session.status)}>
                    {session.status}
                  </Badge>
                  <Button size="sm" variant="outline">
                    {session.type === 'video' ? 'Join Call' : 'View Details'}
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {upcomingSessions.length === 0 && (
            <div className="text-center py-8">
              <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No upcoming sessions scheduled</p>
              <Link to="/dashboard/mentorship/sessions">
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Schedule Your First Session
                </Button>
              </Link>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Sessions</p>
                  <p className="text-2xl font-bold">{stats.completedSessions}</p>
                  <p className="text-sm text-green-600">All time</p>
                </div>
                <Clock className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Response Rate</p>
                  <p className="text-2xl font-bold">{stats.responseRate}%</p>
                  <p className="text-sm text-green-600">Excellent</p>
                </div>
                <MessageSquare className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Success Rate</p>
                  <p className="text-2xl font-bold">87%</p>
                  <p className="text-sm text-green-600">Mentee completion</p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default MentorDashboard;
