import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  BusinessPlan, 
  BusinessPlanSection,
  businessPlansAPI,
  businessPlanSectionsAPI 
} from '../services/businessPlanApi';
import { queryKeys, createOptimisticUpdate, handleQueryError } from '../lib/react-query';
import { validateBusinessPlanContent, validateBusinessPlanTitle } from '../utils/security';

/**
 * Hook for fetching all business plans
 */
export const useBusinessPlans = () => {
  return useQuery({
    queryKey: queryKeys.businessPlans,
    queryFn: businessPlansAPI.getBusinessPlans,
    staleTime: 2 * 60 * 1000, // 2 minutes
    select: (data) => {
      // Sort by updated date, most recent first
      return data.sort((a, b) => 
        new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
      );
    },
  });
};

/**
 * Hook for fetching a single business plan
 */
export const useBusinessPlan = (id: string | number) => {
  return useQuery({
    queryKey: queryKeys.businessPlan(id),
    queryFn: () => businessPlansAPI.getBusinessPlan(Number(id)),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on 404 errors
      if (error && typeof error === 'object' && 'status' in error && error.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

/**
 * Hook for fetching business plan sections
 */
export const useBusinessPlanSections = (planId: string | number) => {
  return useQuery({
    queryKey: queryKeys.businessPlanSections(planId),
    queryFn: () => businessPlanSectionsAPI.getSections(Number(planId)),
    enabled: !!planId,
    staleTime: 3 * 60 * 1000, // 3 minutes
    select: (data) => {
      // Sort sections by order
      return data.sort((a, b) => (a.order || 0) - (b.order || 0));
    },
  });
};

/**
 * Hook for creating a new business plan
 */
export const useCreateBusinessPlan = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: Partial<BusinessPlan>) => {
      // Validate title before creating
      if (data.title) {
        const { isValid, sanitized, error } = validateBusinessPlanTitle(data.title);
        if (!isValid) {
          throw new Error(error || 'Invalid title');
        }
        data.title = sanitized;
      }
      
      return businessPlansAPI.createBusinessPlan(data);
    },
    ...createOptimisticUpdate<BusinessPlan[]>(
      queryKeys.businessPlans,
      (oldData = []) => [
        {
          id: Date.now(), // Temporary ID
          title: data.title || 'New Business Plan',
          description: data.description || '',
          completion_percentage: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          business_idea: data.business_idea || null,
          user: data.user || null,
        } as BusinessPlan,
        ...oldData,
      ]
    ),
    onSuccess: (newPlan) => {
      // Update the business plans list
      queryClient.setQueryData<BusinessPlan[]>(
        queryKeys.businessPlans,
        (oldData = []) => [newPlan, ...oldData.filter(plan => plan.id !== newPlan.id)]
      );
      
      // Set the new plan data
      queryClient.setQueryData(queryKeys.businessPlan(newPlan.id), newPlan);
    },
    onError: (error) => {
      console.error('Failed to create business plan:', handleQueryError(error));
    },
  });
};

/**
 * Hook for updating a business plan
 */
export const useUpdateBusinessPlan = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: Partial<BusinessPlan> }) => {
      // Validate title if provided
      if (data.title) {
        const { isValid, sanitized, error } = validateBusinessPlanTitle(data.title);
        if (!isValid) {
          throw new Error(error || 'Invalid title');
        }
        data.title = sanitized;
      }
      
      return businessPlansAPI.updateBusinessPlan(id, data);
    },
    ...createOptimisticUpdate<BusinessPlan>(
      queryKeys.businessPlan(variables.id),
      (oldData) => oldData ? { ...oldData, ...variables.data } : oldData
    ),
    onSuccess: (updatedPlan) => {
      // Update the business plan
      queryClient.setQueryData(queryKeys.businessPlan(updatedPlan.id), updatedPlan);
      
      // Update the business plans list
      queryClient.setQueryData<BusinessPlan[]>(
        queryKeys.businessPlans,
        (oldData = []) => oldData.map(plan => 
          plan.id === updatedPlan.id ? updatedPlan : plan
        )
      );
    },
    onError: (error) => {
      console.error('Failed to update business plan:', handleQueryError(error));
    },
  });
};

/**
 * Hook for deleting a business plan
 */
export const useDeleteBusinessPlan = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: businessPlansAPI.deleteBusinessPlan,
    ...createOptimisticUpdate<BusinessPlan[]>(
      queryKeys.businessPlans,
      (oldData = []) => oldData.filter(plan => plan.id !== planId)
    ),
    onSuccess: (_, planId) => {
      // Remove from business plans list
      queryClient.setQueryData<BusinessPlan[]>(
        queryKeys.businessPlans,
        (oldData = []) => oldData.filter(plan => plan.id !== planId)
      );
      
      // Remove the individual plan data
      queryClient.removeQueries({ queryKey: queryKeys.businessPlan(planId) });
      queryClient.removeQueries({ queryKey: queryKeys.businessPlanSections(planId) });
    },
    onError: (error) => {
      console.error('Failed to delete business plan:', handleQueryError(error));
    },
  });
};

/**
 * Hook for updating a business plan section
 */
export const useUpdateBusinessPlanSection = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      sectionId, 
      content, 
      planId 
    }: { 
      sectionId: number; 
      content: string; 
      planId: number;
    }) => {
      // Validate and sanitize content
      const { isValid, sanitized, error } = validateBusinessPlanContent(content);
      if (!isValid) {
        throw new Error(error || 'Invalid content');
      }
      
      return businessPlanSectionsAPI.updateSection(sectionId, { content: sanitized });
    },
    ...createOptimisticUpdate<BusinessPlanSection[]>(
      queryKeys.businessPlanSections(variables.planId),
      (oldData = []) => oldData.map(section => 
        section.id === variables.sectionId 
          ? { ...section, content: variables.content, updated_at: new Date().toISOString() }
          : section
      )
    ),
    onSuccess: (updatedSection, { planId }) => {
      // Update the sections list
      queryClient.setQueryData<BusinessPlanSection[]>(
        queryKeys.businessPlanSections(planId),
        (oldData = []) => oldData.map(section => 
          section.id === updatedSection.id ? updatedSection : section
        )
      );
      
      // Update business plan completion percentage
      queryClient.invalidateQueries({ queryKey: queryKeys.businessPlan(planId) });
    },
    onError: (error) => {
      console.error('Failed to update section:', handleQueryError(error));
    },
  });
};

/**
 * Hook for auto-saving section content with debouncing
 */
export const useAutoSaveSection = () => {
  const updateSection = useUpdateBusinessPlanSection();
  
  return {
    autoSave: updateSection.mutate,
    isAutoSaving: updateSection.isPending,
    autoSaveError: updateSection.error,
  };
};

/**
 * Hook for prefetching related data
 */
export const usePrefetchBusinessPlanData = () => {
  const queryClient = useQueryClient();
  
  const prefetchBusinessPlan = async (id: number) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.businessPlan(id),
      queryFn: () => businessPlansAPI.getBusinessPlan(id),
      staleTime: 5 * 60 * 1000,
    });
  };
  
  const prefetchBusinessPlanSections = async (planId: number) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.businessPlanSections(planId),
      queryFn: () => businessPlanSectionsAPI.getSections(planId),
      staleTime: 3 * 60 * 1000,
    });
  };
  
  return {
    prefetchBusinessPlan,
    prefetchBusinessPlanSections,
  };
};

/**
 * Hook for business plan analytics
 */
export const useBusinessPlanAnalytics = (planId: string | number) => {
  return useQuery({
    queryKey: queryKeys.businessPlanAnalytics(planId),
    queryFn: async () => {
      // This would call an analytics API endpoint
      // For now, we'll calculate basic analytics from the plan data
      const plan = await businessPlansAPI.getBusinessPlan(Number(planId));
      const sections = await businessPlanSectionsAPI.getSections(Number(planId));
      
      const completedSections = sections.filter(s => s.content && s.content.trim().length > 0);
      const totalWords = sections.reduce((total, section) => {
        const words = section.content ? section.content.replace(/<[^>]*>/g, '').split(/\s+/).length : 0;
        return total + words;
      }, 0);
      
      return {
        completionPercentage: plan.completion_percentage || 0,
        totalSections: sections.length,
        completedSections: completedSections.length,
        totalWords,
        lastUpdated: plan.updated_at,
        timeSpent: 0, // Would come from tracking
      };
    },
    enabled: !!planId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};