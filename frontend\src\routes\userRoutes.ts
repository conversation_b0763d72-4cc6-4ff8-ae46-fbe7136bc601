import { lazy } from 'react';
import { RouteConfig, createUserRoute, createAIRoute } from './routeConfig';

// DEDICATED USER DASHBOARD - Regular users only
const UserDashboardPage = lazy(() => import('../pages/dashboard/UserDashboardPage'));
// Legacy UserDashboard kept for backward compatibility
const UserDashboard = lazy(() => import('../components/dashboard/user-dashboard/UserDashboard'));

// Fixed syntax error in BusinessIdeasPage - now loading directly
const BusinessIdeasPage = lazy(() => import('../pages/dashboard/BusinessIdeasPage'));

const BusinessPlanPage = lazy(() => import('../pages/dashboard/BusinessPlanPage'));
const BusinessPlanPreviewPage = lazy(() => import('../pages/dashboard/BusinessPlanPreviewPage'));
const BusinessPlanAnalyticsPage = lazy(() => import('../pages/dashboard/BusinessPlanAnalyticsPage'));
const TemplatesOverviewPage = lazy(() => import('../pages/dashboard/TemplatesOverviewPage'));
const TemplateCreationPage = lazy(() => import('../pages/dashboard/TemplateCreationPage'));
const TemplateAnalyticsPage = lazy(() => import('../pages/dashboard/TemplateAnalyticsPage'));
const CollaborativeTemplatesPage = lazy(() => import('../pages/dashboard/CollaborativeTemplatesPage'));
const AITemplateGeneratorPage = lazy(() => import('../pages/dashboard/AITemplateGeneratorPage'));
const TemplateLibraryPage = lazy(() => import('../pages/dashboard/TemplateLibraryPage'));
const TemplateComparisonPage = lazy(() => import('../pages/dashboard/TemplateComparisonPage'));
const AdvancedSearchPage = lazy(() => import('../pages/dashboard/AdvancedSearchPage'));
const PostsPage = lazy(() => import('../pages/dashboard/PostsPage'));
const EventsPage = lazy(() => import('../pages/dashboard/EventsPage'));
const ResourcesPage = lazy(() => import('../pages/dashboard/ResourcesPage'));
const UserAnalyticsPage = lazy(() => import('../pages/dashboard/UserAnalyticsPage'));
const EnhancedAnalyticsPage = lazy(() => import('../pages/dashboard/EnhancedAnalyticsPage'));
const UserProfilePage = lazy(() => import('../pages/UserProfilePage'));
const BusinessIncubator = lazy(() => import('../components/incubator/BusinessIncubator'));
const UserSettings = lazy(() => import('../pages/UserSettings'));
const ConsolidatedAIPage = lazy(() => import('../pages/ConsolidatedAIPage'));

/**
 * ✅ ESSENTIAL USER ROUTES - Core working pages that match sidebar
 */
export const userRoutes: RouteConfig[] = [
  // Main dashboard - DEDICATED USER ONLY ✅ (using UserDashboardPage)
  createUserRoute('/dashboard', UserDashboardPage, 'Loading dashboard...'),

  // Business ideas - VERIFIED ✅
  createUserRoute('/dashboard/business-ideas', BusinessIdeasPage, 'Loading business ideas...'),
  createUserRoute('/dashboard/business-ideas/new', BusinessIdeasPage, 'Loading business idea creator...'),

  // Business plans - NEW ✅
  createUserRoute('/dashboard/business-plans', BusinessPlanPage, 'Loading business plans...'),
  createUserRoute('/dashboard/business-plans/new', BusinessPlanPage, 'Loading business plan creator...'),
  createUserRoute('/dashboard/business-plans/:id', BusinessPlanPage, 'Loading business plan...'),
  createUserRoute('/dashboard/business-plans/:id/preview', BusinessPlanPreviewPage, 'Loading business plan preview...'),
  createUserRoute('/dashboard/business-plans/:id/analytics', BusinessPlanAnalyticsPage, 'Loading business plan analytics...'),

  // Templates - VERIFIED ✅
  createUserRoute('/dashboard/templates', TemplatesOverviewPage, 'Loading templates...'),
  createUserRoute('/dashboard/templates/create', TemplateCreationPage, 'Loading template creation...'),
  createUserRoute('/dashboard/templates/analytics', TemplateAnalyticsPage, 'Loading template analytics...'),
  createUserRoute('/dashboard/templates/collaborate', CollaborativeTemplatesPage, 'Loading collaborative templates...'),
  createUserRoute('/dashboard/templates/ai-generator', AITemplateGeneratorPage, 'Loading AI template generator...'),
  createUserRoute('/dashboard/templates/library', TemplateLibraryPage, 'Loading template library...'),
  createUserRoute('/dashboard/templates/compare', TemplateComparisonPage, 'Loading template comparison...'),
  createUserRoute('/dashboard/templates/search', AdvancedSearchPage, 'Loading advanced search...'),

  // Posts - VERIFIED ✅
  createUserRoute('/dashboard/posts', PostsPage, 'Loading posts...'),

  // Events - VERIFIED ✅
  createUserRoute('/dashboard/events', EventsPage, 'Loading events...'),

  // Resources - VERIFIED ✅
  createUserRoute('/dashboard/resources', ResourcesPage, 'Loading resources...'),

  // Analytics - NEW ✅
  createUserRoute('/dashboard/analytics', UserAnalyticsPage, 'Loading analytics...'),

  // Enhanced Analytics for specific business ideas - NEW ✅
  createUserRoute('/dashboard/analytics/:id', EnhancedAnalyticsPage, 'Loading enhanced analytics...'),

  // Profile - VERIFIED ✅
  createUserRoute('/profile', UserProfilePage, 'Loading profile...'),

  // Incubator - VERIFIED ✅ (public route /incubator exists, keeping only dashboard version)
  createUserRoute('/dashboard/incubator', BusinessIncubator, 'Loading dashboard incubator...'),

  // ✅ MOVED: Mentorship routes moved to mentorRoutes.ts
  // ✅ MOVED: Investment routes moved to investorRoutes.ts

  // AI Features - VERIFIED ✅ (for regular users only, role-specific AI access in dedicated route files)
  createAIRoute('/chat/enhanced', ConsolidatedAIPage, 'Loading AI Assistant...', ['user', 'admin', 'super_admin']),
  createAIRoute('/chat/enhancedBusiness', ConsolidatedAIPage, 'Loading Enhanced Business Analysis...', ['user', 'admin', 'super_admin']),
  createAIRoute('/chat/enhancedBusiness/:businessIdeaId', ConsolidatedAIPage, 'Loading Business Analysis...', ['user', 'admin', 'super_admin']),

  // Settings - VERIFIED ✅
  createUserRoute('/settings', UserSettings, 'Loading settings...'),
];

export default userRoutes;
