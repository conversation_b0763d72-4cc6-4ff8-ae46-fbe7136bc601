/**
 * AI-Powered Business Idea Generator
 * Generates innovative business ideas based on user preferences and market trends
 */

import React, { useState, useEffect } from 'react';
import {
  Lightbulb,
  Brain,
  Sparkles,
  RefreshCw,
  Star,
  ArrowRight,
  Settings
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { RTLText, RTLFlex } from '../common';
import { centralizedAiApi } from '../../services/centralizedAiApi';
import { aiErrorHandler } from '../../services/aiErrorHandler';

interface BusinessIdea {
  id: string;
  title: string;
  description: string;
  industry: string;
  targetMarket: string;
  revenueModel: string;
  startupCost: 'low' | 'medium' | 'high';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  marketPotential: number; // 1-5 stars
  innovationScore: number; // 1-5 stars
  timeToMarket: string;
  keyFeatures: string[];
  competitiveAdvantage: string;
  risks: string[];
  opportunities: string[];
  nextSteps: string[];
  aiConfidence: number; // 1-100
  trending: boolean;
  tags: string[];
}

interface GenerationPreferences {
  industries: string[];
  budget: 'low' | 'medium' | 'high' | 'any';
  experience: 'beginner' | 'intermediate' | 'advanced' | 'any';
  timeCommitment: 'part-time' | 'full-time' | 'any';
  riskTolerance: 'low' | 'medium' | 'high';
  location: string;
  interests: string[];
  skills: string[];
  marketFocus: 'local' | 'national' | 'international';
}

export const BusinessIdeaGenerator: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [ideas, setIdeas] = useState<BusinessIdea[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);
  const [selectedIdea, setSelectedIdea] = useState<BusinessIdea | null>(null);
  const [generationCount, setGenerationCount] = useState(0);

  const [preferences, setPreferences] = useState<GenerationPreferences>({
    industries: [],
    budget: 'any',
    experience: 'any',
    timeCommitment: 'any',
    riskTolerance: 'medium',
    location: '',
    interests: [],
    skills: [],
    marketFocus: 'national'
  });

  // Industry options
  const industries = [
    'Technology', 'Healthcare', 'Education', 'Finance', 'E-commerce',
    'Food & Beverage', 'Entertainment', 'Real Estate', 'Transportation', 'Sustainability', 'Fashion', 'Sports & Fitness', 'Travel', 'Agriculture'
  ];



  // Generate real business ideas using AI service
  const generateBusinessIdeas = async () => {
    setIsGenerating(true);
    setError(null);

    // Use AI error handler for robust generation
    const result = await aiErrorHandler.generateBusinessIdeas({
      industry: selectedIndustry,
      interests: interests.split(',').map(i => i.trim()).filter(Boolean),
      skills: skills.split(',').map(s => s.trim()).filter(Boolean),
      budget: budget,
      location: location
    });

    if (result.success && result.data) {
      // Parse AI response to extract business ideas
      const aiContent = result.data.content || '';
      const generatedIdeas = parseAIBusinessIdeas(aiContent, selectedIndustry);

      setIdeas(generatedIdeas);
      setGenerationCount(prev => prev + 1);

      // Show fallback notice if using fallback
      if (result.isFallback) {
        setError(`Using fallback ideas: ${result.fallbackReason}`);
      }
    } else {
      setError(result.error || 'Failed to generate business ideas');
      setIdeas([]);
    }

    setIsGenerating(false);

  };

  // Helper function to parse AI response into business ideas
  const parseAIBusinessIdeas = (aiContent: string, industry: string): BusinessIdea[] => {
    try {
      // Try to parse as JSON first
      const parsed = JSON.parse(aiContent);
      if (Array.isArray(parsed)) {
        return parsed.map((idea, index) => ({
          id: `ai-${Date.now()}-${index}`,
          title: idea.title || `${industry} Business Idea ${index + 1}`,
          description: idea.description || 'AI-generated business idea',
          industry: idea.industry || industry,
          targetMarket: idea.targetMarket || idea.target_market || 'General market',
          revenueModel: idea.revenueModel || idea.revenue_model || 'Subscription',
          startupCost: idea.startupCost || idea.startup_cost || 'medium',
          difficulty: idea.difficulty || 'intermediate',
          marketPotential: idea.marketPotential || idea.market_potential || 4,
          innovationScore: idea.innovationScore || idea.innovation_score || 4,
          timeToMarket: idea.timeToMarket || idea.time_to_market || '6-12 months',
          keyFeatures: idea.keyFeatures || idea.key_features || [],
          competitiveAdvantage: idea.competitiveAdvantage || idea.competitive_advantage || '',
          risks: idea.risks || [],
          opportunities: idea.opportunities || [],
          nextSteps: idea.nextSteps || idea.next_steps || [],
          aiConfidence: idea.aiConfidence || idea.ai_confidence || 0.8,
          trending: idea.trending || false,
          tags: idea.tags || []
        }));
      }
    } catch (e) {
      // If not JSON, extract ideas from text
      const ideas = extractIdeasFromText(aiContent, industry);
      if (ideas.length > 0) return ideas;
    }

    // Fallback to single idea from content
    return [{
      id: `ai-${Date.now()}`,
      title: `${industry} Business Opportunity`,
      description: aiContent.substring(0, 200) + '...',
      industry,
      targetMarket: 'General market',
      revenueModel: 'Subscription',
      startupCost: 'medium' as const,
      difficulty: 'intermediate' as const,
      marketPotential: 4,
      innovationScore: 4,
      timeToMarket: '6-12 months',
      keyFeatures: [],
      competitiveAdvantage: '',
      risks: [],
      opportunities: [],
      nextSteps: [],
      aiConfidence: 0.7,
      trending: false,
      tags: []
    }];
  };

  // Helper function to extract ideas from text content
  const extractIdeasFromText = (text: string, industry: string): BusinessIdea[] => {
    const ideas: BusinessIdea[] = [];
    const lines = text.split('\n').filter(line => line.trim());

    let currentIdea: Partial<BusinessIdea> = {};
    let ideaCount = 0;

    for (const line of lines) {
      const trimmed = line.trim();

      // Look for idea titles (lines that start with numbers or bullets)
      if (/^[\d\-\*\•]/.test(trimmed) && trimmed.length > 10) {
        // Save previous idea if exists
        if (currentIdea.title) {
          ideas.push(createBusinessIdeaFromPartial(currentIdea, industry, ideaCount++));
        }

        // Start new idea
        currentIdea = {
          title: trimmed.replace(/^[\d\-\*\•\s]+/, '').trim()
        };
      } else if (trimmed.length > 20 && !currentIdea.description) {
        // Use as description
        currentIdea.description = trimmed;
      }
    }

    // Add last idea
    if (currentIdea.title) {
      ideas.push(createBusinessIdeaFromPartial(currentIdea, industry, ideaCount));
    }

    return ideas.slice(0, 5); // Limit to 5 ideas
  };

  // Helper function to create complete BusinessIdea from partial data
  const createBusinessIdeaFromPartial = (partial: Partial<BusinessIdea>, industry: string, index: number): BusinessIdea => ({
    id: `ai-${Date.now()}-${index}`,
    title: partial.title || `${industry} Business Idea ${index + 1}`,
    description: partial.description || 'AI-generated business opportunity',
    industry,
    targetMarket: 'General market',
    revenueModel: 'Subscription',
    startupCost: 'medium' as const,
    difficulty: 'intermediate' as const,
    marketPotential: 4,
    innovationScore: 4,
    timeToMarket: '6-12 months',
    keyFeatures: [],
    competitiveAdvantage: '',
    risks: [],
    opportunities: [],
    nextSteps: [],
    aiConfidence: 0.7,
    trending: false,
    tags: []
  });

  // Fallback ideas when AI service fails
  const generateFallbackIdeas = (industry: string): BusinessIdea[] => {
    const fallbackIdeas = [
      {
        title: `${industry} Digital Platform`,
        description: `A digital platform connecting ${industry.toLowerCase()} professionals with customers.`,
        revenueModel: 'Commission-based'
      },
      {
        title: `AI-Powered ${industry} Assistant`,
        description: `An AI assistant for ${industry.toLowerCase()} professionals to automate tasks.`,
        revenueModel: 'Subscription'
      }
    ];

    return fallbackIdeas.map((idea, index) => ({
      id: `fallback-${Date.now()}-${index}`,
      title: idea.title,
      description: idea.description,
      industry,
      targetMarket: 'Small to medium businesses',
      revenueModel: idea.revenueModel,
      startupCost: 'medium' as const,
      difficulty: 'intermediate' as const,
      marketPotential: 3,
      innovationScore: 3,
      timeToMarket: '6-12 months',
      keyFeatures: [],
      competitiveAdvantage: '',
      risks: [],
      opportunities: [],
      nextSteps: [],
      aiConfidence: 0.5,
      trending: false,
      tags: ['fallback']
    }));
  };

  // Filter ideas based on preferences
  const filteredIdeas = ideas.filter(idea => {
    if (preferences.industries.length > 0) {
      const matchesIndustry = preferences.industries.some(industry =>
        idea.industry.toLowerCase().includes(industry.toLowerCase())
      );
      if (!matchesIndustry) return false;
    }

    if (preferences.budget !== 'any' && idea.startupCost !== preferences.budget) {
      return false;
    }

    if (preferences.experience !== 'any' && idea.difficulty !== preferences.experience) {
      return false;
    }

    return true;
  });

  const getStartupCostColor = (cost: string) => {
    switch (cost) {
      case 'low': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'high': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-400';
      case 'intermediate': return 'text-yellow-400';
      case 'advanced': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-600'}
      />
    ));
  };

  useEffect(() => {
    // Generate initial ideas on component mount
    generateBusinessIdeas();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <RTLFlex className="items-center justify-center mb-4">
          <Lightbulb className="text-yellow-400" size={32} />
          <RTLText className={`text-2xl font-bold text-white ${isRTL ? 'mr-3' : 'ml-3'}`}>
            {t('businessIdeas.title')}
          </RTLText>
        </RTLFlex>
        <p className="text-gray-300 max-w-2xl mx-auto">
          {t('businessIdeas.description')}
        </p>
      </div>

      {/* Controls */}
      <RTLFlex className="items-center justify-between">
        <RTLFlex className="items-center gap-4">
          <button
            onClick={generateBusinessIdeas}
            disabled={isGenerating}
            className={`flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 text-white rounded-lg font-medium transition-all ${isRTL ? "flex-row-reverse" : ""}`}
          >
            {isGenerating ? (
              <RefreshCw className="animate-spin" size={20} />
            ) : (
              <Sparkles size={20} />
            )}
            <span className={isRTL ? 'mr-2' : 'ml-2'}>
              {isGenerating ? t('businessIdeas.generatingIdeas') : t('businessIdeas.generateNewIdeas')}
            </span>
          </button>

          <button
            onClick={() => setShowPreferences(!showPreferences)}
            className={`flex items-center px-4 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <Settings size={20} />
            <span className={isRTL ? 'mr-2' : 'ml-2'}>{t('businessIdeas.preferences')}</span>
          </button>
        </RTLFlex>

        <div className="text-gray-400 text-sm">
          {filteredIdeas.length} {t('businessIdeas.ideasGenerated')} • {t('businessIdeas.round')} {generationCount}
        </div>
      </RTLFlex>

      {/* Preferences Panel */}
      {showPreferences && (
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">{t('businessIdeas.generationPreferences')}</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Industries */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('businessIdeas.preferredIndustries')}
              </label>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {industries.map(industry => (
                  <label key={industry} className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <input
                      type="checkbox"
                      checked={preferences.industries.includes(industry)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setPreferences(prev => ({
                            ...prev,
                            industries: [...prev.industries, industry]
                          }));
                        } else {
                          setPreferences(prev => ({
                            ...prev,
                            industries: prev.industries.filter(i => i !== industry)
                          }));
                        }
                      }}
                      className={`mr-2 rounded ${isRTL ? "space-x-reverse" : ""}`}
                    />
                    <span className="text-sm text-gray-300">{industry}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Budget */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('businessIdeas.startupBudget')}
              </label>
              <select
                value={preferences.budget}
                onChange={(e) => setPreferences(prev => ({ ...prev, budget: e.target.value as any }))}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500"
              >
                <option value="any">{t('businessIdeas.anyBudget')}</option>
                <option value="low">{t('businessIdeas.lowBudget')}</option>
                <option value="medium">{t('businessIdeas.mediumBudget')}</option>
                <option value="high">{t('businessIdeas.highBudget')}</option>
              </select>
            </div>

            {/* Experience */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                {t('businessIdeas.experienceLevel')}
              </label>
              <select
                value={preferences.experience}
                onChange={(e) => setPreferences(prev => ({ ...prev, experience: e.target.value as any }))}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-purple-500"
              >
                <option value="any">{t('businessIdeas.anyLevel')}</option>
                <option value="beginner">{t('businessIdeas.beginner')}</option>
                <option value="intermediate">{t('businessIdeas.intermediate')}</option>
                <option value="advanced">{t('businessIdeas.advanced')}</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Ideas Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredIdeas.map((idea) => (
          <div
            key={idea.id}
            className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 hover:border-purple-500/50 transition-all cursor-pointer"
            onClick={() => setSelectedIdea(idea)}
          >
            {/* Header */}
            <RTLFlex className="items-start justify-between mb-4">
              <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                <RTLFlex className="items-center mb-2">
                  <h3 className="text-lg font-semibold text-white">{idea.title}</h3>
                  {idea.trending && (
                    <span className={`px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded-full ml-2 ${isRTL ? "space-x-reverse" : ""}`}>
                      {t('businessIdeas.trending')}
                    </span>
                  )}
                </RTLFlex>
                <p className="text-gray-300 text-sm mb-3">{idea.description}</p>
              </div>
            </RTLFlex>

            {/* Metrics */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <div className="text-xs text-gray-500 mb-1">{t('businessIdeas.marketPotential')}</div>
                <RTLFlex className="items-center">
                  {renderStars(idea.marketPotential)}
                </RTLFlex>
              </div>
              <div>
                <div className="text-xs text-gray-500 mb-1">{t('businessIdeas.innovationScore')}</div>
                <RTLFlex className="items-center">
                  {renderStars(idea.innovationScore)}
                </RTLFlex>
              </div>
            </div>

            {/* Details */}
            <div className="space-y-2 mb-4">
              <RTLFlex className="items-center justify-between text-sm">
                <span className="text-gray-400">{t('businessIdeas.startupCost')}</span>
                <span className={`font-medium ${getStartupCostColor(idea.startupCost)}`}>
                  {idea.startupCost.charAt(0).toUpperCase() + idea.startupCost.slice(1)}
                </span>
              </RTLFlex>
              <RTLFlex className="items-center justify-between text-sm">
                <span className="text-gray-400">{t('businessIdeas.difficulty')}</span>
                <span className={`font-medium ${getDifficultyColor(idea.difficulty)}`}>
                  {idea.difficulty.charAt(0).toUpperCase() + idea.difficulty.slice(1)}
                </span>
              </RTLFlex>
              <RTLFlex className="items-center justify-between text-sm">
                <span className="text-gray-400">{t('businessIdeas.timeToMarket')}</span>
                <span className="text-white font-medium">{idea.timeToMarket}</span>
              </RTLFlex>
            </div>

            {/* Tags */}
            <RTLFlex className={`items-center gap-2 mb-4 flex-wrap ${isRTL ? "flex-row-reverse" : ""}`}>
              {idea.tags.slice(0, 3).map(tag => (
                <span
                  key={tag}
                  className="px-2 py-1 bg-purple-500/20 text-purple-300 text-xs rounded-full"
                >
                  {tag}
                </span>
              ))}
            </RTLFlex>

            {/* AI Confidence */}
            <RTLFlex className="items-center justify-between">
              <RTLFlex className="items-center text-sm text-gray-400">
                <Brain size={16} />
                <span className={isRTL ? 'mr-2' : 'ml-2'}>
                  {t('businessIdeas.aiConfidence')} {idea.aiConfidence}%
                </span>
              </RTLFlex>
              <ArrowRight className="text-purple-400" size={16} />
            </RTLFlex>
          </div>
        ))}
      </div>

      {/* Detailed View Modal */}
      {selectedIdea && (
        <div className={`fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-gray-900 rounded-xl border border-gray-700 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Header */}
              <RTLFlex className="items-start justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-white mb-2">{selectedIdea.title}</h2>
                  <p className="text-gray-300">{selectedIdea.description}</p>
                </div>
                <button
                  onClick={() => setSelectedIdea(null)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  ✕
                </button>
              </RTLFlex>

              {/* Content sections would continue here... */}
              <div className="text-center py-8">
                <p className="text-gray-400">
                  {t('businessIdeas.detailedViewDescription')}
                </p>
                <button
                  onClick={() => setSelectedIdea(null)}
                  className="mt-4 px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  {t('businessIdeas.close')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {filteredIdeas.length === 0 && !isGenerating && (
        <div className="text-center py-12">
          <Lightbulb className="mx-auto mb-4 text-gray-600" size={48} />
          <h3 className="text-xl font-semibold text-gray-400 mb-2">{t('businessIdeas.noIdeasFound')}</h3>
          <p className="text-gray-500 mb-4">
            {t('businessIdeas.adjustPreferences')}
          </p>
          <button
            onClick={generateBusinessIdeas}
            className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
          >
            {t('businessIdeas.generateIdeas')}
          </button>
        </div>
      )}
    </div>
  );
};

export default BusinessIdeaGenerator;
