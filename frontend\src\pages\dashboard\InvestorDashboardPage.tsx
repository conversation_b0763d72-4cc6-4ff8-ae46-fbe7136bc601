import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import {
  TrendingUp,
  DollarSign,
  PieChart,
  Target,
  Briefcase,
  Star,
  Eye,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Building
} from 'lucide-react';
import { investorDashboardAPI } from '../../services/investorDashboardApi';

interface InvestorStats {
  totalInvestments: number;
  activeDeals: number;
  portfolioValue: number;
  roi: number;
  dealsThisMonth: number;
  pendingReviews: number;
}

interface InvestmentOpportunity {
  id: string;
  companyName: string;
  industry: string;
  stage: string;
  fundingGoal: number;
  currentFunding: number;
  valuation: number;
  roi_projection: number;
  risk_level: 'low' | 'medium' | 'high';
  description: string;
  deadline: string;
}

interface PortfolioItem {
  id: string;
  companyName: string;
  industry: string;
  investmentAmount: number;
  currentValue: number;
  roi: number;
  status: 'growing' | 'stable' | 'declining';
  lastUpdate: string;
}

const InvestorDashboardPage: React.FC = () => {
  const { t } = useTranslation();
  const [stats, setStats] = useState<InvestorStats>({
    totalInvestments: 0,
    activeDeals: 0,
    portfolioValue: 0,
    roi: 0,
    dealsThisMonth: 0,
    pendingReviews: 0
  });
  const [opportunities, setOpportunities] = useState<InvestmentOpportunity[]>([]);
  const [portfolio, setPortfolio] = useState<PortfolioItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchInvestorData = async () => {
      try {
        setLoading(true);

        // Fetch real investor dashboard stats
        const dashboardStats = await investorDashboardAPI.getDashboardStats();
        setStats(dashboardStats);

        // Fetch investment opportunities
        const opportunitiesData = await investorDashboardAPI.getInvestmentOpportunities();
        setOpportunities(opportunitiesData);

        // Fetch portfolio investments
        const portfolioData = await investorDashboardAPI.getPortfolioInvestments();

        // Transform portfolio data to match the expected format
        const transformedPortfolio = portfolioData.map(investment => ({
          id: investment.id,
          companyName: investment.companyName,
          industry: investment.industry,
          investmentAmount: investment.investmentAmount,
          currentValue: investment.currentValue,
          roi: investment.performance,
          status: investment.performance > 0 ? 'growing' : 'declining',
          lastUpdate: investment.lastUpdate
        }));

        setPortfolio(transformedPortfolio);

      } catch (error) {
        console.error('Error fetching investor dashboard data:', error);

        // Fallback to empty data on error
        setStats({
          totalInvestments: 0,
          activeDeals: 0,
          portfolioValue: 0,
          roi: 0,
          dealsThisMonth: 0,
          pendingReviews: 0
        });
        setOpportunities([]);
        setPortfolio([]);
      } finally {
        setLoading(false);
      }
    };

    fetchInvestorData();
  }, []);

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'growing': return <ArrowUpRight className="h-4 w-4 text-green-500" />;
      case 'declining': return <ArrowDownRight className="h-4 w-4 text-red-500" />;
      default: return <Target className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <AuthenticatedLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">{t('Loading investor dashboard...')}</p>
          </div>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            <div className="p-2 bg-purple-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">{t('Investor Dashboard')}</h1>
              <p className="text-gray-300">{t('Track investments and discover opportunities')}</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-400">{t('Portfolio Value')}</p>
              <p className="text-2xl font-bold text-purple-400">{formatCurrency(stats.portfolioValue)}</p>
            </div>
          </div>
        </div>
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">{t('Total Investments')}</p>
                <p className="text-2xl font-bold text-purple-400">{stats.totalInvestments}</p>
              </div>
              <Briefcase className="h-8 w-8 text-purple-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">{t('Active Deals')}</p>
                <p className="text-2xl font-bold text-blue-400">{stats.activeDeals}</p>
              </div>
              <Building className="h-8 w-8 text-blue-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">{t('Average ROI')}</p>
                <p className="text-2xl font-bold text-green-400">{stats.roi}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-400" />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">{t('Pending Reviews')}</p>
                <p className="text-2xl font-bold text-orange-400">{stats.pendingReviews}</p>
              </div>
              <Eye className="h-8 w-8 text-orange-400" />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Investment Opportunities */}
          <div className="bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 overflow-hidden">
            <div className="px-6 py-4 border-b border-white/20">
              <h2 className="text-lg font-semibold text-white">{t('Investment Opportunities')}</h2>
            </div>
            <div className="divide-y divide-white/20">
              {opportunities.map((opportunity) => (
                <div key={opportunity.id} className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="font-medium text-white">{opportunity.companyName}</h3>
                      <p className="text-sm text-gray-300">{opportunity.industry} • {opportunity.stage}</p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(opportunity.risk_level)}`}>
                      {opportunity.risk_level.toUpperCase()} RISK
                    </span>
                  </div>

                  <p className="text-sm text-gray-300 mb-3">{opportunity.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
                    <div>
                      <span className="text-gray-400">{t('Funding Goal')}: </span>
                      <span className="font-medium text-white">{formatCurrency(opportunity.fundingGoal)}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">{t('Valuation')}: </span>
                      <span className="font-medium text-white">{formatCurrency(opportunity.valuation)}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">{t('ROI Projection')}: </span>
                      <span className="font-medium text-green-400">{opportunity.roi_projection}%</span>
                    </div>
                    <div>
                      <span className="text-gray-400">{t('Deadline')}: </span>
                      <span className="font-medium text-white">{new Date(opportunity.deadline).toLocaleDateString()}</span>
                    </div>
                  </div>

                  <div className="mb-3">
                    <div className="flex justify-between text-sm text-gray-400 mb-1">
                      <span>{t('Funding Progress')}</span>
                      <span>{Math.round((opportunity.currentFunding / opportunity.fundingGoal) * 100)}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(opportunity.currentFunding / opportunity.fundingGoal) * 100}%` }}
                      ></div>
                    </div>
                  </div>

                  <button className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors text-sm">
                    {t('View Details')}
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Portfolio */}
          <div className="bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 overflow-hidden">
            <div className="px-6 py-4 border-b border-white/20">
              <h2 className="text-lg font-semibold text-white">{t('My Portfolio')}</h2>
            </div>
            <div className="divide-y divide-white/20">
              {portfolio.map((item) => (
                <div key={item.id} className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="font-medium text-white">{item.companyName}</h3>
                      <p className="text-sm text-gray-300">{item.industry}</p>
                    </div>
                    <div className="flex items-center space-x-1">
                      {getStatusIcon(item.status)}
                      <span className={`text-sm font-medium ${item.roi >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {item.roi >= 0 ? '+' : ''}{item.roi.toFixed(1)}%
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">{t('Investment')}: </span>
                      <span className="font-medium text-white">{formatCurrency(item.investmentAmount)}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">{t('Current Value')}: </span>
                      <span className="font-medium text-white">{formatCurrency(item.currentValue)}</span>
                    </div>
                  </div>

                  <div className="mt-3 text-xs text-gray-400">
                    {t('Last updated')}: {new Date(item.lastUpdate).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default InvestorDashboardPage;
