import React from 'react';
import { ArrowLeft, Download, BarChart2, Eye, Save, RefreshCw } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { BusinessPlan } from '../../services/businessPlanApi';

interface BusinessPlanHeaderProps {
  businessPlan: BusinessPlan;
  onBack: () => void;
  onSave: () => void;
  onExport: () => void;
  onViewAnalytics: () => void;
  onPreview: () => void;
  isSaving: boolean;
  hasUnsavedChanges: boolean;
  completionPercentage: number;
}

/**
 * Business Plan Header Component
 * Contains navigation, title, actions, and progress information
 */
const BusinessPlanHeader: React.FC<BusinessPlanHeaderProps> = ({
  businessPlan,
  onBack,
  onSave,
  onExport,
  onViewAnalytics,
  onPreview,
  isSaving,
  hasUnsavedChanges,
  completionPercentage
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  return (
    <div className="business-plan-header glass-light rounded-md p-6 mb-6 shadow-sm">
      {/* Top row: Navigation and actions */}
      <div className="flex justify-between items-start mb-4">
        {/* Back button */}
        <button
          onClick={onBack}
          className="flex items-center text-glass-secondary hover:text-glass-primary transition-colors"
          aria-label={t("common.back")}
        >
          <ArrowLeft size={20} className={`${isRTL ? 'rotate-180' : ''} mr-2`} />
          {t("businessPlan.backToPlans")}
        </button>

        {/* Action buttons */}
        <div className="flex items-center space-x-3">
          {/* Save button */}
          <button
            onClick={onSave}
            disabled={!hasUnsavedChanges || isSaving}
            className={`flex items-center px-4 py-2 rounded-md transition-colors ${
              hasUnsavedChanges && !isSaving
                ? "bg-purple-600 hover:bg-purple-700 text-white"
                : "bg-glass-border text-glass-secondary cursor-not-allowed"
            }`}
            aria-label={t("common.save")}
          >
            {isSaving ? (
              <RefreshCw size={16} className="mr-2 animate-spin" />
            ) : (
              <Save size={16} className="mr-2" />
            )}
            {isSaving ? t("businessPlan.saving") : t("common.save")}
          </button>

          {/* Preview button */}
          <button
            onClick={onPreview}
            className="flex items-center px-4 py-2 rounded-md bg-glass-hover hover:bg-glass-border text-glass-primary transition-colors"
            aria-label={t("businessPlan.preview")}
          >
            <Eye size={16} className="mr-2" />
            {t("businessPlan.preview")}
          </button>

          {/* Analytics button */}
          <button
            onClick={onViewAnalytics}
            className="flex items-center px-4 py-2 rounded-md bg-glass-hover hover:bg-glass-border text-glass-primary transition-colors"
            aria-label={t("businessPlan.analytics.viewAnalytics")}
          >
            <BarChart2 size={16} className="mr-2" />
            {t("businessPlan.analytics.viewAnalytics")}
          </button>

          {/* Export button */}
          <button
            onClick={onExport}
            className="flex items-center px-4 py-2 rounded-md bg-glass-hover hover:bg-glass-border text-glass-primary transition-colors"
            aria-label={t("businessPlan.export")}
          >
            <Download size={16} className="mr-2" />
            {t("businessPlan.export")}
          </button>
        </div>
      </div>

      {/* Business plan title and info */}
      <div className="mb-4">
        <h1 className="text-2xl font-bold text-glass-primary mb-2">
          {businessPlan.title}
        </h1>
        {businessPlan.description && (
          <p className="text-glass-secondary">
            {businessPlan.description}
          </p>
        )}
      </div>

      {/* Progress and status */}
      <div className="flex items-center justify-between">
        {/* Progress bar */}
        <div className="flex items-center flex-1 mr-6">
          <span className="text-sm text-glass-secondary mr-3">
            {t("businessPlan.progress")}:
          </span>
          <div className="flex-1 bg-glass-border rounded-full h-2 overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-300"
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>
          <span className="text-sm text-glass-primary ml-3 font-medium">
            {completionPercentage}%
          </span>
        </div>

        {/* Status indicators */}
        <div className="flex items-center space-x-4 text-sm">
          {/* Unsaved changes indicator */}
          {hasUnsavedChanges && (
            <span className="text-amber-400 flex items-center">
              <div className="w-2 h-2 bg-amber-400 rounded-full mr-2"></div>
              {t("businessPlan.unsavedChanges")}
            </span>
          )}

          {/* Last updated */}
          <span className="text-glass-secondary">
            {t("businessPlan.lastUpdated")}: {new Date(businessPlan.updated_at).toLocaleDateString()}
          </span>

          {/* Business idea link */}
          {businessPlan.business_idea && (
            <span className="text-glass-secondary">
              {t("businessPlan.linkedToIdea")}: {businessPlan.business_idea.title}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default BusinessPlanHeader;